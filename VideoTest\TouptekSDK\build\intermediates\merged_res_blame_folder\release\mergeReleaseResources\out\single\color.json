[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\material_on_surface_emphasis_medium.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\material_on_surface_emphasis_medium.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\abc_search_url_text.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-appcompat-1.7.0-12:\\color\\abc_search_url_text.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_fab_ripple_color_selector.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_fab_ripple_color_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\abc_secondary_text_material_light.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-appcompat-1.7.0-12:\\color\\abc_secondary_text_material_light.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\material_on_background_disabled.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\material_on_background_disabled.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\material_timepicker_button_stroke.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\material_timepicker_button_stroke.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_radiobutton_ripple_tint.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_radiobutton_ripple_tint.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_navigation_item_icon_tint.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_navigation_item_icon_tint.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_fab_ripple_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_fab_ripple_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_tabs_legacy_text_color_selector.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_tabs_legacy_text_color_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_switch_track_tint.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_switch_track_tint.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_fab_efab_background_color_selector.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_fab_efab_background_color_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_dark_hint_foreground.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_dark_hint_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\material_on_surface_disabled.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\material_on_surface_disabled.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_timepicker_button_text_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_timepicker_button_text_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_icon_button_icon_color_selector.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_icon_button_icon_color_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_checkbox_button_tint.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_checkbox_button_tint.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_default_color_secondary_text.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_default_color_secondary_text.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_tabs_ripple_color_secondary.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_tabs_ripple_color_secondary.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_assist_chip_icon_tint_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_assist_chip_icon_tint_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\material_timepicker_clock_text_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\material_timepicker_clock_text_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\material_slider_active_tick_marks_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\material_slider_active_tick_marks_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\material_on_primary_emphasis_high_type.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\material_on_primary_emphasis_high_type.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_textfield_stroke_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_textfield_stroke_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_card_ripple_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_card_ripple_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_btn_bg_color_selector.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_btn_bg_color_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\material_on_background_emphasis_medium.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\material_on_background_emphasis_medium.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\abc_secondary_text_material_dark.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-appcompat-1.7.0-12:\\color\\abc_secondary_text_material_dark.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\abc_background_cache_hint_selector_material_dark.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-appcompat-1.7.0-12:\\color\\abc_background_cache_hint_selector_material_dark.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_switch_thumb_icon_tint.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_switch_thumb_icon_tint.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\abc_hint_foreground_material_light.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-appcompat-1.7.0-12:\\color\\abc_hint_foreground_material_light.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_navigation_item_background_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_navigation_item_background_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_navigation_item_ripple_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_navigation_item_ripple_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_chip_text_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_chip_text_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_appbar_overlay_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_appbar_overlay_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_choice_chip_background_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_choice_chip_background_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\material_on_surface_stroke.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\material_on_surface_stroke.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\abc_primary_text_disable_only_material_dark.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-appcompat-1.7.0-12:\\color\\abc_primary_text_disable_only_material_dark.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_timepicker_display_background_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_timepicker_display_background_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_bottom_sheet_drag_handle_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_bottom_sheet_drag_handle_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_switch_thumb_tint.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_switch_thumb_tint.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_navigation_bar_ripple_color_selector.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_navigation_bar_ripple_color_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_btn_text_color_selector.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_btn_text_color_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_calendar_selected_range.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_calendar_selected_range.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\material_personalized_primary_inverse_text_disable_only.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\material_personalized_primary_inverse_text_disable_only.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_card_view_ripple.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_card_view_ripple.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_tabs_icon_color_selector_colored.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_tabs_icon_color_selector_colored.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_switch_thumb_tint.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_switch_thumb_tint.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_fab_efab_foreground_color_selector.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_fab_efab_foreground_color_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_navigation_bar_colored_ripple_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_navigation_bar_colored_ripple_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_navigation_bar_ripple_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_navigation_bar_ripple_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_tabs_text_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_tabs_text_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_timepicker_display_text_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_timepicker_display_text_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\material_timepicker_button_background.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\material_timepicker_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_filled_icon_button_container_color_selector.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_filled_icon_button_container_color_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\material_slider_inactive_tick_marks_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\material_slider_inactive_tick_marks_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_outlined_stroke_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_outlined_stroke_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_text_btn_text_color_selector.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_text_btn_text_color_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\material_personalized_color_primary_text.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\material_personalized_color_primary_text.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_radiobutton_button_tint.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_radiobutton_button_tint.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_calendar_item_disabled_text.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_calendar_item_disabled_text.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\material_personalized_color_secondary_text.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\material_personalized_color_secondary_text.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_fab_icon_text_color_selector.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_fab_icon_text_color_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_textfield_indicator_text_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_textfield_indicator_text_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_navigation_bar_colored_item_tint.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_navigation_bar_colored_item_tint.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_indicator_text_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_indicator_text_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\switch_thumb_material_dark.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-appcompat-1.7.0-12:\\color\\switch_thumb_material_dark.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_tonal_button_ripple_color_selector.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_tonal_button_ripple_color_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_dark_primary_text_disable_only.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_dark_primary_text_disable_only.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\material_personalized_hint_foreground_inverse.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\material_personalized_hint_foreground_inverse.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_hint_foreground.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_hint_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_switch_track_decoration_tint.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_switch_track_decoration_tint.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_card_stroke_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_card_stroke_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_button_ripple_color_selector.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_button_ripple_color_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_timepicker_button_ripple_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_timepicker_button_ripple_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_button_ripple_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_button_ripple_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\switch_thumb_material_light.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-appcompat-1.7.0-12:\\color\\switch_thumb_material_light.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_dark_default_color_secondary_text.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_dark_default_color_secondary_text.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_timepicker_time_input_stroke_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_timepicker_time_input_stroke_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_filled_background_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_filled_background_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_btn_ripple_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_btn_ripple_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_tabs_icon_color_selector.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_tabs_icon_color_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_tabs_text_color_secondary.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_tabs_text_color_secondary.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_chip_close_icon_tint.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_chip_close_icon_tint.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_chip_assist_text_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_chip_assist_text_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_on_primary_text_btn_text_color_selector.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_on_primary_text_btn_text_color_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_fab_bg_color_selector.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_fab_bg_color_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_tabs_colored_ripple_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_tabs_colored_ripple_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\material_on_surface_emphasis_high_type.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\material_on_surface_emphasis_high_type.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_card_view_foreground.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_card_view_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_textfield_label_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_textfield_label_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_btn_text_btn_ripple_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_btn_text_btn_ripple_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_timepicker_clock_text_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_timepicker_clock_text_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_navigation_rail_item_with_indicator_icon_tint.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_navigation_rail_item_with_indicator_icon_tint.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_card_foreground_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_card_foreground_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_choice_chip_text_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_choice_chip_text_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_tabs_ripple_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_tabs_ripple_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_textfield_input_text_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_textfield_input_text_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_choice_chip_ripple_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_choice_chip_ripple_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_efab_ripple_color_selector.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_efab_ripple_color_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_switch_track_tint.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_switch_track_tint.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_assist_chip_stroke_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_assist_chip_stroke_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\design_icon_tint.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\design_icon_tint.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\abc_background_cache_hint_selector_material_light.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-appcompat-1.7.0-12:\\color\\abc_background_cache_hint_selector_material_light.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_textfield_filled_background_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_textfield_filled_background_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_dark_highlighted_text.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_dark_highlighted_text.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_navigation_item_text_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_navigation_item_text_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\abc_primary_text_material_light.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-appcompat-1.7.0-12:\\color\\abc_primary_text_material_light.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_simple_item_ripple_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_simple_item_ripple_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_navigation_rail_ripple_color_selector.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_navigation_rail_ripple_color_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\material_cursor_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\material_cursor_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_tabs_icon_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_tabs_icon_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_error.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_error.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_navigation_rail_item_with_indicator_label_tint.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_navigation_rail_item_with_indicator_label_tint.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\design_box_stroke_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\design_box_stroke_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_popupmenu_overlay_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_popupmenu_overlay_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_filled_stroke_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_filled_stroke_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_primary_text_disable_only.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_primary_text_disable_only.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\material_personalized_color_secondary_text_inverse.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\material_personalized_color_secondary_text_inverse.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_navigation_bar_item_with_indicator_icon_tint.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_navigation_bar_item_with_indicator_icon_tint.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_chip_surface_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_chip_surface_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_slider_halo_color_legacy.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_slider_halo_color_legacy.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\material_on_primary_disabled.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\material_on_primary_disabled.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_timepicker_secondary_text_button_text_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_timepicker_secondary_text_button_text_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_tabs_icon_color_secondary.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_tabs_icon_color_secondary.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_navigation_item_icon_tint.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_navigation_item_icon_tint.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_chip_stroke_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_chip_stroke_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_navigation_bar_item_with_indicator_label_tint.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_navigation_bar_item_with_indicator_label_tint.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_chip_background_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_chip_background_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_text_button_ripple_color_selector.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_text_button_ripple_color_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\material_divider_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\material_divider_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_navigation_item_text_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_navigation_item_text_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_calendar_item_stroke_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_calendar_item_stroke_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_timepicker_display_ripple_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_timepicker_display_ripple_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_selection_control_ripple_color_selector.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_selection_control_ripple_color_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_chip_text_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_chip_text_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_elevated_chip_background_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_elevated_chip_background_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_slider_active_track_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_slider_active_track_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_slider_thumb_color_legacy.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_slider_thumb_color_legacy.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_popupmenu_overlay_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_popupmenu_overlay_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_timepicker_secondary_text_button_ripple_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_timepicker_secondary_text_button_ripple_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_btn_text_btn_bg_color_selector.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_btn_text_btn_bg_color_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_outlined_icon_tint.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_outlined_icon_tint.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\material_slider_inactive_track_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\material_slider_inactive_track_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\abc_primary_text_disable_only_material_light.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-appcompat-1.7.0-12:\\color\\abc_primary_text_disable_only_material_light.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_slider_thumb_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_slider_thumb_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_navigation_bar_item_tint.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_navigation_bar_item_tint.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\material_personalized_color_primary_text_inverse.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\material_personalized_color_primary_text_inverse.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_timepicker_button_background_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_timepicker_button_background_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\tp_video_button_text_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-main-36:\\color\\tp_video_button_text_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\material_on_background_emphasis_high_type.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\material_on_background_emphasis_high_type.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\abc_hint_foreground_material_dark.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-appcompat-1.7.0-12:\\color\\abc_hint_foreground_material_dark.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_slider_active_track_color_legacy.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_slider_active_track_color_legacy.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_chip_background_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_chip_background_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\material_personalized_primary_text_disable_only.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\material_personalized_primary_text_disable_only.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\material_personalized__highlighted_text_inverse.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\material_personalized__highlighted_text_inverse.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_button_foreground_color_selector.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_button_foreground_color_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_checkbox_button_icon_tint.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_checkbox_button_icon_tint.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_slider_inactive_track_color_legacy.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_slider_inactive_track_color_legacy.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_button_outline_color_selector.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_button_outline_color_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_calendar_item_stroke_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_calendar_item_stroke_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_on_surface_ripple_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_on_surface_ripple_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\material_slider_thumb_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\material_slider_thumb_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_text_button_background_color_selector.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_text_button_background_color_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\material_timepicker_modebutton_tint.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\material_timepicker_modebutton_tint.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_chip_ripple_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_chip_ripple_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\material_on_primary_emphasis_medium.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\material_on_primary_emphasis_medium.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\material_slider_halo_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\material_slider_halo_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\abc_primary_text_material_dark.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-appcompat-1.7.0-12:\\color\\abc_primary_text_material_dark.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\material_slider_active_track_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\material_slider_active_track_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_btn_stroke_color_selector.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_btn_stroke_color_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_filled_icon_tint.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_filled_icon_tint.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_dark_default_color_primary_text.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_dark_default_color_primary_text.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_button_background_color_selector.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_button_background_color_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_slider_inactive_track_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_slider_inactive_track_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_text_button_foreground_color_selector.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_text_button_foreground_color_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_default_color_primary_text.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_default_color_primary_text.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\material_personalized_hint_foreground.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\material_personalized_hint_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\mtrl_navigation_item_background_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\mtrl_navigation_item_background_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\material_personalized__highlighted_text.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\material_personalized__highlighted_text.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\material_timepicker_clockface.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\material_timepicker_clockface.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_highlighted_text.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_highlighted_text.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\m3_tabs_ripple_color.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\m3_tabs_ripple_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-release-35:\\color\\design_error.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.TouptekSDK-material-1.12.0-24:\\color\\design_error.xml"}]