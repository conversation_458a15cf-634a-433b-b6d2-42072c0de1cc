[{"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/drawable_tp_video_button_background.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/drawable/tp_video_button_background.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/drawable_tp_video_progress_drawable.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/drawable/tp_video_progress_drawable.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/drawable_tp_speed_item_selected_background.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/drawable/tp_speed_item_selected_background.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/drawable_ic_skip_next_white_24.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/drawable/ic_skip_next_white_24.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/anim_tp_speed_dialog_enter.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/anim/tp_speed_dialog_enter.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/drawable_tp_speed_item_background.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/drawable/tp_speed_item_background.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/layout_media_browser_integrated.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/layout/media_browser_integrated.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/drawable_ic_play_arrow_white_24.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/drawable/ic_play_arrow_white_24.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/layout_fragment_tp_smb_settings.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/layout/fragment_tp_smb_settings.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/layout_spinner_item.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/layout/spinner_item.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/drawable_tp_video_controls_background.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/drawable/tp_video_controls_background.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/mipmap-anydpi_ic_launcher_round.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/mipmap-anydpi/ic_launcher_round.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/layout_item_settings_menu.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/layout/item_settings_menu.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/drawable_tp_speed_dropdown_background.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/drawable/tp_speed_dropdown_background.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/layout_image_viewer.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/layout/image_viewer.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/layout_network_settings.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/layout/network_settings.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/drawable_ic_fast_rewind_white_24.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/drawable/ic_fast_rewind_white_24.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/drawable_tp_video_progress_thumb.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/drawable/tp_video_progress_thumb.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/layout_activity_tp_video_player_new.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/layout/activity_tp_video_player_new.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/layout_media_item.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/layout/media_item.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/drawable_dialog_background.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/drawable/dialog_background.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/animator_tp_video_button_release.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/animator/tp_video_button_release.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/drawable_ic_skip_previous_white_24.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/drawable/ic_skip_previous_white_24.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/drawable_tp_video_play_button_background.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/drawable/tp_video_play_button_background.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/color_tp_video_button_text_color.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/color/tp_video_button_text_color.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/layout_dialog_tp_settings.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/layout/dialog_tp_settings.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/animator_tp_video_button_press.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/animator/tp_video_button_press.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/layout_tp_speed_selection_dialog.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/layout/tp_speed_selection_dialog.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/drawable_tp_speed_menu_background.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/drawable/tp_speed_menu_background.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/drawable_ic_launcher_background.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/drawable/ic_launcher_background.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/layout_dialog_tp_test.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/layout/dialog_tp_test.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/anim_tp_speed_dialog_exit.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/anim/tp_speed_dialog_exit.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/drawable_tp_video_settings_button_background.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/drawable/tp_video_settings_button_background.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/layout_dialog_smb_settings.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/layout/dialog_smb_settings.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/anim_tp_speed_dropdown_enter.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/anim/tp_speed_dropdown_enter.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/layout_fragment_tp_tv_mode_settings.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/layout/fragment_tp_tv_mode_settings.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/layout_tp_speed_dropdown_menu.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/layout/tp_speed_dropdown_menu.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/mipmap-anydpi_ic_launcher.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/mipmap-anydpi/ic_launcher.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/drawable_ic_pause_white_24.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/drawable/ic_pause_white_24.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/layout_fragment_tp_network_settings.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/layout/fragment_tp_network_settings.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/xml_backup_rules.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/xml/backup_rules.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/layout_popup_menu.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/layout/popup_menu.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/drawable_ic_settings_white_24.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/drawable/ic_settings_white_24.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/layout_tp_video_player_controls.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/layout/tp_video_player_controls.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/xml_data_extraction_rules.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/xml/data_extraction_rules.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/layout_activity_main.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/layout/activity_main.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/drawable_ic_fast_forward_white_24.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/drawable/ic_fast_forward_white_24.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/layout_media_browser.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/layout/media_browser.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/layout_decoder.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/layout/decoder.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/anim_tp_speed_dropdown_exit.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/anim/tp_speed_dropdown_exit.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/layout_encoder.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/layout/encoder.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/drawable_ic_launcher_foreground.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/drawable_ic_step_frame_white_24.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/drawable/ic_step_frame_white_24.xml"}, {"merged": "com.android.rockchip.mediacodecnew.app-debug-38:/layout_dialog_image_format_settings.xml.flat", "source": "com.android.rockchip.mediacodecnew.app-main-40:/layout/dialog_image_format_settings.xml"}]