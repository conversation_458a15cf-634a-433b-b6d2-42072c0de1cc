# compiler: R8
# compiler_version: 8.6.17
# common_typos_disable
# {"id":"com.android.tools.r8.mapping","version":"2.2"}
# pg_map_id: 4a49f2e
# pg_map_hash: SHA-256 4a49f2e9d2ae297dcc34d2f822c065e70ad2c3fe11ae2d5c056dca6cc7b4344d
com.android.rockchip.camera2.rtsp.RTSPManager -> com.android.rockchip.camera2.rtsp.RTSPManager:
# {"id":"sourceFile","fileName":"RTSPManager.java"}
    1:1:void <init>():169:169 -> <init>
    2:40:void <init>():88:126 -> <init>
    1:1:com.android.rockchip.camera2.rtsp.RTSPManager$StreamType getCurrentStreamType():554:554 -> getCurrentStreamType
    1:5:com.android.rockchip.camera2.rtsp.RTSPManager getInstance():179:183 -> getInstance
    1:16:java.lang.String getIpAddressForInterface(java.lang.String):448:463 -> getIpAddressForInterface
    1:1:java.lang.String getNetworkInterface():358:358 -> getNetworkInterface
    1:1:java.lang.String getStreamUrl():544:544 -> getStreamUrl
    1:20:void handleScreenCaptureResult(androidx.activity.result.ActivityResult):498:517 -> handleScreenCaptureResult
    1:1:boolean hasScreenCapturePermission():290:290 -> hasScreenCapturePermission
    1:1:void initStreamer():527:527 -> initStreamer
    1:12:com.android.rockchip.camera2.rtsp.RTSPManager initialize(androidx.appcompat.app.AppCompatActivity):195:206 -> initialize
    1:1:boolean isStreaming():564:564 -> isStreaming
    1:1:com.android.rockchip.camera2.rtsp.RTSPManager onPermissionGranted(java.util.function.Consumer):279:279 -> onPermissionGranted
    1:1:com.android.rockchip.camera2.rtsp.RTSPManager onStreamError(java.util.function.Consumer):267:267 -> onStreamError
    1:1:com.android.rockchip.camera2.rtsp.RTSPManager onStreamStarted(java.util.function.Consumer):243:243 -> onStreamStarted
    1:1:com.android.rockchip.camera2.rtsp.RTSPManager onStreamStopped(java.lang.Runnable):255:255 -> onStreamStopped
    1:3:void release():573:575 -> release
    1:4:void requestScreenCapturePermission():485:488 -> requestScreenCapturePermission
    1:13:boolean requestScreenPermission():301:313 -> requestScreenPermission
    1:1:com.android.rockchip.camera2.rtsp.RTSPManager setConfig(com.android.rockchip.camera2.rtsp.config.RTSPConfig):218:218 -> setConfig
    1:2:com.android.rockchip.camera2.rtsp.RTSPManager setNetworkInterface(java.lang.String):347:348 -> setNetworkInterface
    1:9:com.android.rockchip.camera2.rtsp.RTSPManager setStreamType(com.android.rockchip.camera2.rtsp.RTSPManager$StreamType):326:334 -> setStreamType
    1:1:com.android.rockchip.camera2.rtsp.RTSPManager setVideoEncoder(com.android.rockchip.camera2.video.VideoEncoder):231:231 -> setVideoEncoder
    1:65:boolean startStreaming():369:433 -> startStreaming
    1:4:void stopStreaming():473:476 -> stopStreaming
com.android.rockchip.camera2.rtsp.RTSPManager$1 -> com.android.rockchip.camera2.rtsp.RTSPManager$1:
# {"id":"sourceFile","fileName":"RTSPManager.java"}
    1:1:void <init>(com.android.rockchip.camera2.rtsp.RTSPManager):127:127 -> <init>
    1:1:void lambda$onPermissionGranted$3(java.lang.String):160:160 -> lambda$onPermissionGranted$3
    1:1:void lambda$onStreamError$2(java.lang.String):151:151 -> lambda$onStreamError$2
    1:1:void lambda$onStreamStarted$0(java.lang.String):133:133 -> lambda$onStreamStarted$0
    1:1:void lambda$onStreamStopped$1():142:142 -> lambda$onStreamStopped$1
    1:3:void onPermissionGranted(java.lang.String):158:160 -> onPermissionGranted
    1:3:void onStreamError(java.lang.String):149:151 -> onStreamError
    1:3:void onStreamStarted(java.lang.String):131:133 -> onStreamStarted
    1:3:void onStreamStopped():140:142 -> onStreamStopped
com.android.rockchip.camera2.rtsp.RTSPManager$StreamStateListener -> com.android.rockchip.camera2.rtsp.RTSPManager$StreamStateListener:
# {"id":"sourceFile","fileName":"RTSPManager.java"}
com.android.rockchip.camera2.rtsp.RTSPManager$StreamType -> com.android.rockchip.camera2.rtsp.RTSPManager$StreamType:
# {"id":"sourceFile","fileName":"RTSPManager.java"}
    1:1:com.android.rockchip.camera2.rtsp.RTSPManager$StreamType[] $values():585:585 -> $values
    1:10:void <clinit>():594:603 -> <clinit>
    11:11:void <clinit>():585:585 -> <clinit>
    1:1:void <init>(java.lang.String,int):585:585 -> <init>
    1:1:com.android.rockchip.camera2.rtsp.RTSPManager$StreamType valueOf(java.lang.String):585:585 -> valueOf
    1:1:com.android.rockchip.camera2.rtsp.RTSPManager$StreamType[] values():585:585 -> values
com.android.rockchip.camera2.rtsp.config.RTSPConfig -> com.android.rockchip.camera2.rtsp.config.RTSPConfig:
# {"id":"sourceFile","fileName":"RTSPConfig.java"}
    1:1:void <init>(int,int,int,int,int,boolean,int):64:64 -> <init>
    2:23:void <init>(int,int,int,int,int,boolean,int):50:71 -> <init>
    1:1:com.android.rockchip.camera2.rtsp.config.RTSPConfig createDefaultConfig():171:171 -> createDefaultConfig
    1:1:int getFrameRate():101:101 -> getFrameRate
    1:1:int getHeight():91:91 -> getHeight
    1:1:java.lang.String getHost():151:151 -> getHost
    1:1:int getKeyFrameInterval():141:141 -> getKeyFrameInterval
    1:1:int getPort():121:121 -> getPort
    1:1:int getVideoBitrate():111:111 -> getVideoBitrate
    1:1:int getWidth():81:81 -> getWidth
    1:1:boolean isRecordMic():131:131 -> isRecordMic
    1:1:void setHost(java.lang.String):161:161 -> setHost
com.android.rockchip.camera2.rtsp.config.RTSPConfig$Builder -> com.android.rockchip.camera2.rtsp.config.RTSPConfig$Builder:
# {"id":"sourceFile","fileName":"RTSPConfig.java"}
    1:1:void <init>():199:199 -> <init>
    2:14:void <init>():181:193 -> <init>
    1:1:com.android.rockchip.camera2.rtsp.config.RTSPConfig build():296:296 -> build
    1:1:com.android.rockchip.camera2.rtsp.config.RTSPConfig$Builder setFrameRate(int):237:237 -> setFrameRate
    1:1:com.android.rockchip.camera2.rtsp.config.RTSPConfig$Builder setKeyFrameInterval(int):285:285 -> setKeyFrameInterval
    1:1:com.android.rockchip.camera2.rtsp.config.RTSPConfig$Builder setPort(int):261:261 -> setPort
    1:1:com.android.rockchip.camera2.rtsp.config.RTSPConfig$Builder setRecordMic(boolean):273:273 -> setRecordMic
    1:2:com.android.rockchip.camera2.rtsp.config.RTSPConfig$Builder setResolution(int,int):211:212 -> setResolution
    3:4:com.android.rockchip.camera2.rtsp.config.RTSPConfig$Builder setResolution(com.android.rockchip.camera2.rtsp.config.RTSPConfig$Resolution):224:225 -> setResolution
    1:1:com.android.rockchip.camera2.rtsp.config.RTSPConfig$Builder setVideoBitrate(int):249:249 -> setVideoBitrate
com.android.rockchip.camera2.rtsp.config.RTSPConfig$Resolution -> com.android.rockchip.camera2.rtsp.config.RTSPConfig$Resolution:
# {"id":"sourceFile","fileName":"RTSPConfig.java"}
    1:1:com.android.rockchip.camera2.rtsp.config.RTSPConfig$Resolution[] $values():312:312 -> $values
    1:9:void <clinit>():315:323 -> <clinit>
    10:10:void <clinit>():312:312 -> <clinit>
    1:3:void <init>(java.lang.String,int,int,int):337:339 -> <init>
    1:1:int getHeight():359:359 -> getHeight
    1:1:int getWidth():349:349 -> getWidth
    1:1:com.android.rockchip.camera2.rtsp.config.RTSPConfig$Resolution valueOf(java.lang.String):312:312 -> valueOf
    1:1:com.android.rockchip.camera2.rtsp.config.RTSPConfig$Resolution[] values():312:312 -> values
com.android.rockchip.camera2.rtsp.encoder.AudioEncoder -> com.android.rockchip.camera2.rtsp.encoder.AudioEncoder:
# {"id":"sourceFile","fileName":"AudioEncoder.java"}
    1:1:void <init>(com.android.rockchip.camera2.rtsp.encoder.AudioEncoder$Builder):58:58 -> <init>
    2:19:void <init>(com.android.rockchip.camera2.rtsp.encoder.AudioEncoder$Builder):51:68 -> <init>
    1:1:com.android.rockchip.camera2.rtsp.encoder.AudioEncoder$Builder builder(android.content.Context,android.media.projection.MediaProjection):73:73 -> builder
    1:37:void encodeAudio(com.pedro.rtspserver.RtspServer,long):182:218 -> encodeAudio
    1:10:void release():232:241 -> release
    1:24:void setupAudioRecord():107:130 -> setupAudioRecord
    25:50:void setupAudioRecord():125:150 -> setupAudioRecord
    1:9:void setupEncoder():162:170 -> setupEncoder
com.android.rockchip.camera2.rtsp.encoder.AudioEncoder$Builder -> com.android.rockchip.camera2.rtsp.encoder.AudioEncoder$Builder:
# {"id":"sourceFile","fileName":"AudioEncoder.java"}
    1:1:void <init>(android.content.Context,android.media.projection.MediaProjection):82:82 -> <init>
    2:7:void <init>(android.content.Context,android.media.projection.MediaProjection):79:84 -> <init>
    1:1:com.android.rockchip.camera2.rtsp.encoder.AudioEncoder build():98:98 -> build
    1:1:com.android.rockchip.camera2.rtsp.encoder.AudioEncoder$Builder recordMicrophone(boolean):88:88 -> recordMicrophone
    1:1:com.android.rockchip.camera2.rtsp.encoder.AudioEncoder$Builder setBufferSize(int):93:93 -> setBufferSize
com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder -> com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder:
# {"id":"sourceFile","fileName":"ScreenVideoEncoder.java"}
    1:1:void <init>(com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder$Builder):62:62 -> <init>
    2:20:void <init>(com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder$Builder):50:68 -> <init>
    1:1:com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder$Builder builder(android.media.projection.MediaProjection):72:72 -> builder
    1:64:void encodeVideoLoop(com.pedro.rtspserver.RtspServer,com.android.rockchip.camera2.rtsp.encoder.AudioEncoder):194:257 -> encodeVideoLoop
    65:69:void encodeVideoLoop(com.pedro.rtspserver.RtspServer,com.android.rockchip.camera2.rtsp.encoder.AudioEncoder):253:257 -> encodeVideoLoop
    70:71:void encodeVideoLoop(com.pedro.rtspserver.RtspServer,com.android.rockchip.camera2.rtsp.encoder.AudioEncoder):257:258 -> encodeVideoLoop
    1:1:void lambda$startEncoding$0(com.pedro.rtspserver.RtspServer,com.android.rockchip.camera2.rtsp.encoder.AudioEncoder):161:161 -> lambda$startEncoding$0
    1:14:void release():266:279 -> release
    1:49:void startEncoding(com.pedro.rtspserver.RtspServer,com.android.rockchip.camera2.rtsp.encoder.AudioEncoder):119:167 -> startEncoding
    1:11:void stopEncoding():175:185 -> stopEncoding
com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder$Builder -> com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder$Builder:
# {"id":"sourceFile","fileName":"ScreenVideoEncoder.java"}
    1:1:void <init>(android.media.projection.MediaProjection):83:83 -> <init>
    2:9:void <init>(android.media.projection.MediaProjection):77:84 -> <init>
    1:1:com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder build():109:109 -> build
    1:1:com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder$Builder setBitrate(int):99:99 -> setBitrate
    1:1:com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder$Builder setFrameRate(int):94:94 -> setFrameRate
    1:1:com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder$Builder setKeyFrameInterval(int):104:104 -> setKeyFrameInterval
    1:2:com.android.rockchip.camera2.rtsp.encoder.ScreenVideoEncoder$Builder setResolution(int,int):88:89 -> setResolution
com.android.rockchip.camera2.rtsp.service.ProjectionData -> com.android.rockchip.camera2.rtsp.service.ProjectionData:
# {"id":"sourceFile","fileName":"ProjectionData.java"}
    1:3:void <init>(int,android.content.Intent):16:18 -> <init>
    1:1:android.content.Intent getData():57:57 -> getData
    1:7:android.media.projection.MediaProjection getMediaProjection(android.content.Context):35:41 -> getMediaProjection
    1:1:int getResultCode():49:49 -> getResultCode
    1:1:boolean isValid():26:26 -> isValid
com.android.rockchip.camera2.rtsp.service.RTSPService -> com.android.rockchip.camera2.rtsp.service.RTSPService:
# {"id":"sourceFile","fileName":"RTSPService.java"}
    1:34:void <init>():29:62 -> <init>
    1:1:android.os.IBinder onBind(android.content.Intent):92:92 -> onBind
    1:1:void onConnectionFailed(java.lang.String):269:269 -> onConnectionFailed
    1:1:void onConnectionStarted(java.lang.String):274:274 -> onConnectionStarted
    1:1:void onCreate():85:85 -> onCreate
    1:2:void onDestroy():105:106 -> onDestroy
    1:2:boolean onUnbind(android.content.Intent):98:99 -> onUnbind
    1:43:void startForeground():209:251 -> startForeground
    1:45:java.lang.String startStreaming(com.android.rockchip.camera2.rtsp.config.RTSPConfig,com.android.rockchip.camera2.rtsp.service.ProjectionData):116:160 -> startStreaming
    1:27:void stopStreaming():175:201 -> stopStreaming
com.android.rockchip.camera2.rtsp.service.RTSPService$RTSPBinder -> com.android.rockchip.camera2.rtsp.service.RTSPService$RTSPBinder:
# {"id":"sourceFile","fileName":"RTSPService.java"}
    com.android.rockchip.camera2.rtsp.service.RTSPService this$0 -> a
    1:1:void <init>(com.android.rockchip.camera2.rtsp.service.RTSPService):67:67 -> <init>
    1:1:java.lang.String getRtspUrl():79:79 -> getRtspUrl
    1:1:com.android.rockchip.camera2.rtsp.service.RTSPService getService():72:72 -> getService
com.android.rockchip.camera2.rtsp.service.RTSPServiceConnection -> com.android.rockchip.camera2.rtsp.service.RTSPServiceConnection:
# {"id":"sourceFile","fileName":"RTSPServiceConnection.java"}
    1:6:void <init>(android.content.Context,com.android.rockchip.camera2.rtsp.config.RTSPConfig,com.android.rockchip.camera2.rtsp.service.ProjectionData,com.android.rockchip.camera2.rtsp.service.RTSPServiceConnection$UrlCallback,java.lang.Runnable):51:56 -> <init>
    1:2:boolean bind():109:110 -> bind
    1:13:void onServiceConnected(android.content.ComponentName,android.os.IBinder):66:78 -> onServiceConnected
    1:2:void onServiceDisconnected(android.content.ComponentName):88:89 -> onServiceDisconnected
    1:3:void release():97:99 -> release
    1:2:void unbind():122:123 -> unbind
com.android.rockchip.camera2.rtsp.service.RTSPServiceConnection$UrlCallback -> com.android.rockchip.camera2.rtsp.service.RTSPServiceConnection$UrlCallback:
# {"id":"sourceFile","fileName":"RTSPServiceConnection.java"}
com.android.rockchip.camera2.rtsp.service.RTSPStreamer -> com.android.rockchip.camera2.rtsp.service.RTSPStreamer:
# {"id":"sourceFile","fileName":"RTSPStreamer.java"}
    1:1:void <init>(android.content.Context,com.android.rockchip.camera2.rtsp.config.RTSPConfig,com.android.rockchip.camera2.rtsp.RTSPManager$StreamType,com.android.rockchip.camera2.rtsp.service.ProjectionData,com.android.rockchip.camera2.video.VideoEncoder,com.android.rockchip.camera2.rtsp.RTSPManager$StreamStateListener):73:73 -> <init>
    2:44:void <init>(android.content.Context,com.android.rockchip.camera2.rtsp.config.RTSPConfig,com.android.rockchip.camera2.rtsp.RTSPManager$StreamType,com.android.rockchip.camera2.rtsp.service.ProjectionData,com.android.rockchip.camera2.video.VideoEncoder,com.android.rockchip.camera2.rtsp.RTSPManager$StreamStateListener):38:80 -> <init>
    1:22:void checkFormatTimeout():485:506 -> checkFormatTimeout
    1:1:java.lang.String getRtspUrl():172:172 -> getRtspUrl
    1:1:com.android.rockchip.camera2.rtsp.RTSPManager$StreamType getStreamType():188:188 -> getStreamType
    1:1:boolean isStreaming():180:180 -> isStreaming
    1:1:void lambda$notifyStreamError$3(java.lang.String):625:625 -> lambda$notifyStreamError$3
    1:1:void lambda$notifyStreamStarted$1(java.lang.String):606:606 -> lambda$notifyStreamStarted$1
    1:1:void lambda$notifyStreamStopped$2():616:616 -> lambda$notifyStreamStopped$2
    1:1:void lambda$startScreenStream$0(java.lang.String):282:282 -> lambda$startScreenStream$0
    1:21:void monitorClientState():413:433 -> monitorClientState
    1:2:void notifyStreamError(java.lang.String):624:625 -> notifyStreamError
    1:4:void notifyStreamStarted(java.lang.String):603:606 -> notifyStreamStarted
    1:3:void notifyStreamStopped():614:616 -> notifyStreamStopped
    1:36:void onVideoDataAvailable(java.nio.ByteBuffer,android.media.MediaCodec$BufferInfo):555:590 -> onVideoDataAvailable
    1:25:void onVideoFormatChanged(android.media.MediaFormat,java.nio.ByteBuffer,java.nio.ByteBuffer):518:542 -> onVideoFormatChanged
    1:2:void requestKeyFrame():472:473 -> requestKeyFrame
    1:20:void resetCameraStreamState():237:256 -> resetCameraStreamState
    1:52:boolean startCameraStream():315:366 -> startCameraStream
    1:7:boolean startCameraStreaming(com.android.rockchip.camera2.video.VideoEncoder,com.android.rockchip.camera2.rtsp.config.RTSPConfig,com.android.rockchip.camera2.rtsp.RTSPManager$StreamStateListener):110:116 -> startCameraStreaming
    1:25:boolean startScreenStream():266:290 -> startScreenStream
    1:7:boolean startScreenStreaming(com.android.rockchip.camera2.rtsp.service.ProjectionData,com.android.rockchip.camera2.rtsp.config.RTSPConfig,com.android.rockchip.camera2.rtsp.RTSPManager$StreamStateListener):128:134 -> startScreenStreaming
    1:10:boolean startStream():88:97 -> startStream
    1:27:void stopCameraStream():377:403 -> stopCameraStream
    1:8:void stopScreenStream():300:307 -> stopScreenStream
    1:9:void stopStream():155:163 -> stopStream
    1:12:boolean switchToCameraStream(com.android.rockchip.camera2.video.VideoEncoder):218:229 -> switchToCameraStream
    1:9:boolean switchToScreenStream(com.android.rockchip.camera2.rtsp.service.ProjectionData):199:207 -> switchToScreenStream
    1:10:void tryGetFormatFromEncoder():446:455 -> tryGetFormatFromEncoder
    1:2:void updateConfig(com.android.rockchip.camera2.rtsp.config.RTSPConfig):145:146 -> updateConfig
com.android.rockchip.camera2.rtsp.service.RTSPStreamer$RtspConnectChecker -> com.android.rockchip.camera2.rtsp.service.a:
# {"id":"sourceFile","fileName":"RTSPStreamer.java"}
    com.android.rockchip.camera2.rtsp.service.RTSPStreamer this$0 -> a
    1:1:void <init>(com.android.rockchip.camera2.rtsp.service.RTSPStreamer):632:632 -> <init>
    1:2:void onConnectionFailed(java.lang.String):650:651 -> onConnectionFailed
    1:1:void onConnectionStarted(java.lang.String):635:635 -> onConnectionStarted
    1:4:void onConnectionSuccess():641:644 -> onConnectionSuccess
    1:1:void onDisconnect():662:662 -> onDisconnect
com.android.rockchip.camera2.service.StreamingService -> com.android.rockchip.camera2.service.StreamingService:
# {"id":"sourceFile","fileName":"StreamingService.java"}
    1:1:void <init>(androidx.appcompat.app.AppCompatActivity,com.android.rockchip.camera2.service.StreamingService$HeartbeatListener):185:185 -> <init>
    2:106:void <init>(androidx.appcompat.app.AppCompatActivity,com.android.rockchip.camera2.service.StreamingService$HeartbeatListener):87:191 -> <init>
    1:32:void configureRtspCallbacks():348:379 -> configureRtspCallbacks
    33:33:void configureRtspCallbacks():350:350 -> configureRtspCallbacks
    1:3:void configureVideoEncoder():574:576 -> configureVideoEncoder
    1:1:com.android.rockchip.camera2.service.StreamingService$StreamType getCurrentStreamType():672:672 -> getCurrentStreamType
    1:1:com.android.rockchip.camera2.rtsp.RTSPManager getRtspManager():1637:1637 -> getRtspManager
    1:1:java.lang.String getStreamUrl():1238:1238 -> getStreamUrl
    1:36:void handleSocketClient(java.net.Socket):821:856 -> handleSocketClient
    37:58:void handleSocketClient(java.net.Socket):835:856 -> handleSocketClient
    59:65:void handleSocketClient(java.net.Socket):846:852 -> handleSocketClient
    66:72:void handleSocketClient(java.net.Socket):852:858 -> handleSocketClient
    1:16:com.android.rockchip.camera2.service.StreamingService initCore(androidx.appcompat.app.AppCompatActivity,com.android.rockchip.camera2.service.StreamingService$HeartbeatListener):229:244 -> initCore
    17:17:com.android.rockchip.camera2.service.StreamingService initCore(androidx.appcompat.app.AppCompatActivity,com.android.rockchip.camera2.service.StreamingService$HeartbeatListener):224:224 -> initCore
    18:18:com.android.rockchip.camera2.service.StreamingService initCore(androidx.appcompat.app.AppCompatActivity,com.android.rockchip.camera2.service.StreamingService$HeartbeatListener):220:220 -> initCore
    1:8:void initRtspManager():331:338 -> initRtspManager
    9:9:void initRtspManager():333:333 -> initRtspManager
    1:33:void initStreaming(com.android.rockchip.camera2.video.VideoEncoder,com.android.rockchip.camera2.video.TpCaptureImage,com.android.rockchip.camera2.service.StreamingService$StreamType):274:306 -> initStreaming
    34:34:void initStreaming(com.android.rockchip.camera2.video.VideoEncoder,com.android.rockchip.camera2.video.TpCaptureImage,com.android.rockchip.camera2.service.StreamingService$StreamType):280:280 -> initStreaming
    35:35:void initStreaming(com.android.rockchip.camera2.video.VideoEncoder,com.android.rockchip.camera2.video.TpCaptureImage,com.android.rockchip.camera2.service.StreamingService$StreamType):276:276 -> initStreaming
    36:36:void initStreaming(com.android.rockchip.camera2.video.VideoEncoder,com.android.rockchip.camera2.video.TpCaptureImage):323:323 -> initStreaming
    1:1:boolean isManualRtspControl():1624:1624 -> isManualRtspControl
    1:1:boolean isRunning():1202:1202 -> isRunning
    1:1:boolean isServicesRunning():1211:1211 -> isServicesRunning
    1:1:boolean isStreaming():1229:1229 -> isStreaming
    1:1:boolean isTpctrlRunning():1220:1220 -> isTpctrlRunning
    1:1:boolean isVideoEncoderReady():1160:1160 -> isVideoEncoderReady
    1:34:boolean killTpctrlProcess():1397:1430 -> killTpctrlProcess
    1:1:void lambda$configureRtspCallbacks$0(java.lang.String):359:359 -> lambda$configureRtspCallbacks$0
    1:5:void lambda$configureRtspCallbacks$1(java.lang.String):357:361 -> lambda$configureRtspCallbacks$1
    1:1:void lambda$configureRtspCallbacks$2():367:367 -> lambda$configureRtspCallbacks$2
    1:3:void lambda$configureRtspCallbacks$3():365:367 -> lambda$configureRtspCallbacks$3
    1:1:void lambda$configureRtspCallbacks$4(java.lang.String):375:375 -> lambda$configureRtspCallbacks$4
    1:5:void lambda$configureRtspCallbacks$5(java.lang.String):373:377 -> lambda$configureRtspCallbacks$5
    1:6:void lambda$configureRtspCallbacks$6(java.lang.String):381:386 -> lambda$configureRtspCallbacks$6
    1:1:void lambda$processSocketConnections$13(java.net.Socket):783:783 -> lambda$processSocketConnections$13
    1:1:void lambda$startImageSocketService$14(java.lang.String):972:972 -> lambda$startImageSocketService$14
    1:1:void lambda$startRtspManually$16(java.lang.String):1506:1506 -> lambda$startRtspManually$16
    1:1:void lambda$startSubServices$15(java.lang.String):1013:1013 -> lambda$startSubServices$15
    1:8:void lambda$startTpctrlConsole$7(java.lang.Process):423:430 -> lambda$startTpctrlConsole$7
    1:23:void lambda$startTpctrlConsole$8():458:480 -> lambda$startTpctrlConsole$8
    1:31:void lambda$stopRtspManually$17():1570:1600 -> lambda$stopRtspManually$17
    1:12:void lambda$updateVideoEncoder$10(com.android.rockchip.camera2.service.StreamingService$StreamType,com.android.rockchip.camera2.video.VideoEncoder):534:545 -> lambda$updateVideoEncoder$10
    1:1:void lambda$updateVideoEncoder$11(com.android.rockchip.camera2.video.VideoEncoder):556:556 -> lambda$updateVideoEncoder$11
    1:1:void lambda$updateVideoEncoder$12(com.android.rockchip.camera2.video.VideoEncoder):562:562 -> lambda$updateVideoEncoder$12
    1:1:void lambda$updateVideoEncoder$9(com.android.rockchip.camera2.video.VideoEncoder):546:546 -> lambda$updateVideoEncoder$9
    1:48:void monitorHeartbeat():884:931 -> monitorHeartbeat
    49:49:void monitorHeartbeat():918:918 -> monitorHeartbeat
    1:10:void onHeartbeatReceived():866:875 -> onHeartbeatReceived
    1:7:void onTpctrlDetected():948:954 -> onTpctrlDetected
    1:1:void onTpctrlLost():990:990 -> onTpctrlLost
    1:36:void processSocketConnections():773:808 -> processSocketConnections
    37:49:void processSocketConnections():796:808 -> processSocketConnections
    50:61:void processSocketConnections():800:811 -> processSocketConnections
    1:8:void release():1247:1254 -> release
    1:17:void sendAllIspConfigs():1266:1282 -> sendAllIspConfigs
    18:36:void sendAllIspConfigs():1281:1299 -> sendAllIspConfigs
    1:56:boolean sendIspConfig(com.android.rockchip.camera2.util.TouptekIspParam,com.android.rockchip.camera2.util.TouptekIspParam$ParamData):1311:1366 -> sendIspConfig
    57:66:boolean sendIspConfig(com.android.rockchip.camera2.util.TouptekIspParam,com.android.rockchip.camera2.util.TouptekIspParam$ParamData):1357:1366 -> sendIspConfig
    67:74:boolean sendIspConfig(com.android.rockchip.camera2.util.TouptekIspParam,com.android.rockchip.camera2.util.TouptekIspParam$ParamData):1366:1373 -> sendIspConfig
    1:2:void setManualRtspControl(boolean):1614:1615 -> setManualRtspControl
    1:33:void setStreamType(com.android.rockchip.camera2.service.StreamingService$StreamType):627:659 -> setStreamType
    1:16:void startImageSocketService():962:977 -> startImageSocketService
    1:100:boolean startRtspManually(com.android.rockchip.camera2.service.StreamingService$StreamType,java.lang.String):1444:1543 -> startRtspManually
    1:35:void startRtspService():1071:1105 -> startRtspService
    1:24:void startRtspServiceWithRetry():1168:1191 -> startRtspServiceWithRetry
    1:25:void startService():588:612 -> startService
    26:26:void startService():590:590 -> startService
    1:9:void startSocketServer():722:730 -> startSocketServer
    1:61:void startSubServices():998:1058 -> startSubServices
    1:91:boolean startTpctrlConsole(java.lang.String):402:492 -> startTpctrlConsole
    1:36:boolean stopRtspManually():1567:1602 -> stopRtspManually
    1:24:void stopService():690:713 -> stopService
    1:25:void stopSocketServer():738:762 -> stopSocketServer
    1:25:void stopSubServices():1123:1147 -> stopSubServices
    1:3:void switchStreamType():680:682 -> switchStreamType
    1:51:void updateVideoEncoder(com.android.rockchip.camera2.video.VideoEncoder):511:561 -> updateVideoEncoder
    1:4:void writeIntToByteArray(byte[],int,int):1381:1384 -> writeIntToByteArray
com.android.rockchip.camera2.service.StreamingService$HeartbeatListener -> com.android.rockchip.camera2.service.StreamingService$HeartbeatListener:
# {"id":"sourceFile","fileName":"StreamingService.java"}
com.android.rockchip.camera2.service.StreamingService$StreamType -> com.android.rockchip.camera2.service.StreamingService$StreamType:
# {"id":"sourceFile","fileName":"StreamingService.java"}
    1:1:com.android.rockchip.camera2.service.StreamingService$StreamType[] $values():151:151 -> $values
    1:3:void <clinit>():154:156 -> <clinit>
    4:4:void <clinit>():151:151 -> <clinit>
    1:1:void <init>(java.lang.String,int):151:151 -> <init>
    1:1:com.android.rockchip.camera2.service.StreamingService$StreamType valueOf(java.lang.String):151:151 -> valueOf
    1:1:com.android.rockchip.camera2.service.StreamingService$StreamType[] values():151:151 -> values
com.android.rockchip.camera2.service.StreamingSocketService -> com.android.rockchip.camera2.service.StreamingSocketService:
# {"id":"sourceFile","fileName":"StreamingSocketService.java"}
    1:1:void <init>(com.android.rockchip.camera2.video.TpCaptureImage,java.lang.String):128:128 -> <init>
    2:57:void <init>(com.android.rockchip.camera2.video.TpCaptureImage,java.lang.String):75:130 -> <init>
    1:121:void captureAndSendImage(java.io.OutputStream,boolean):359:479 -> captureAndSendImage
    122:165:void captureAndSendImage(java.io.OutputStream,boolean):436:479 -> captureAndSendImage
    166:198:void captureAndSendImage(java.io.OutputStream,boolean):447:479 -> captureAndSendImage
    199:207:void captureAndSendImage(java.io.OutputStream,boolean):467:475 -> captureAndSendImage
    208:214:void captureAndSendImage(java.io.OutputStream,boolean):475:481 -> captureAndSendImage
    1:60:void handleClient(java.net.Socket):283:342 -> handleClient
    61:105:void handleClient(java.net.Socket):298:342 -> handleClient
    106:135:void handleClient(java.net.Socket):313:342 -> handleClient
    136:142:void handleClient(java.net.Socket):331:337 -> handleClient
    143:150:void handleClient(java.net.Socket):337:344 -> handleClient
    1:40:void handleIspParamCommand(java.io.InputStream):572:611 -> handleIspParamCommand
    41:54:void handleIspParamCommand(java.io.InputStream):608:621 -> handleIspParamCommand
    55:55:void handleIspParamCommand(java.io.InputStream):621:621 -> handleIspParamCommand
    1:1:boolean isRunning():150:150 -> isRunning
    1:24:void lambda$captureAndSendImage$1(android.util.Size,java.util.concurrent.atomic.AtomicBoolean,java.util.concurrent.atomic.AtomicBoolean):383:406 -> lambda$captureAndSendImage$1
    1:1:void lambda$runServer$0(java.net.Socket):240:240 -> lambda$runServer$0
    1:43:void lambda$sendIspParameterToTpctrl$2(com.android.rockchip.camera2.util.TouptekIspParam,int):678:720 -> lambda$sendIspParameterToTpctrl$2
    44:52:void lambda$sendIspParameterToTpctrl$2(com.android.rockchip.camera2.util.TouptekIspParam,int):708:716 -> lambda$sendIspParameterToTpctrl$2
    53:60:void lambda$sendIspParameterToTpctrl$2(com.android.rockchip.camera2.util.TouptekIspParam,int):716:723 -> lambda$sendIspParameterToTpctrl$2
    1:4:void onDataChanged(com.android.rockchip.camera2.util.TouptekIspParam,int):647:650 -> onDataChanged
    1:1:void onLongDataChanged(com.android.rockchip.camera2.util.TouptekIspParam,long):659:659 -> onLongDataChanged
    1:4:void processIspParam(int,int):633:636 -> processIspParam
    5:8:void processIspParam(int,int):635:638 -> processIspParam
    1:38:void runServer():228:265 -> runServer
    39:51:void runServer():253:265 -> runServer
    52:63:void runServer():257:268 -> runServer
    1:1:void sendIspParameterToTpctrl(com.android.rockchip.camera2.util.TouptekIspParam,int):672:672 -> sendIspParameterToTpctrl
    1:29:void sendJpegData(java.io.OutputStream,int,int,byte[]):499:527 -> sendJpegData
    1:17:void sendRedImage(java.io.OutputStream,boolean):545:561 -> sendRedImage
    1:1:void setLogListener(com.android.rockchip.camera2.service.StreamingSocketService$LogListener):140:140 -> setLogListener
    1:12:void start():162:173 -> start
    1:30:void stop():186:215 -> stop
    1:4:void writeIntToBytes(byte[],int,int):736:739 -> writeIntToBytes
com.android.rockchip.camera2.service.StreamingSocketService$1 -> com.android.rockchip.camera2.service.StreamingSocketService$1:
# {"id":"sourceFile","fileName":"StreamingSocketService.java"}
    1:1:void <init>(com.android.rockchip.camera2.service.StreamingSocketService,java.util.concurrent.atomic.AtomicBoolean,java.util.concurrent.atomic.AtomicBoolean):386:386 -> <init>
    1:2:void onError(java.lang.String):398:399 -> onError
    1:3:void onImageSaved(java.lang.String):390:392 -> onImageSaved
com.android.rockchip.camera2.service.StreamingSocketService$LogListener -> com.android.rockchip.camera2.service.StreamingSocketService$LogListener:
# {"id":"sourceFile","fileName":"StreamingSocketService.java"}
com.android.rockchip.camera2.util.FileStorageUtils -> com.android.rockchip.camera2.util.FileStorageUtils:
# {"id":"sourceFile","fileName":"FileStorageUtils.java"}
    1:1:void <init>():62:62 -> <init>
    1:9:java.lang.String convertToActualPath(java.lang.String):497:505 -> convertToActualPath
    1:1:java.lang.String createImagePath(android.content.Context):323:323 -> createImagePath
    2:62:java.lang.String createImagePath(android.content.Context,java.lang.String,boolean,java.lang.String):344:404 -> createImagePath
    1:46:java.lang.String createVideoPath(android.content.Context):261:306 -> createVideoPath
    1:41:java.lang.String generateUniqueFileName(java.lang.String):592:632 -> generateUniqueFileName
    1:9:long getAvailableStorageSpace(java.lang.String):426:434 -> getAvailableStorageSpace
    1:13:java.lang.String getExternalStoragePath(android.content.Context):528:540 -> getExternalStoragePath
    1:27:java.lang.String getFileSystemType(java.lang.String):454:480 -> getFileSystemType
    1:7:java.lang.String getInternalStoragePath(android.content.Context):564:570 -> getInternalStoragePath
    1:5:boolean isRemovableStorage(android.content.Context,java.lang.String):234:238 -> isRemovableStorage
    1:75:void startUsbDriveMonitor(android.content.Context,com.android.rockchip.camera2.util.FileStorageUtils$StorageListener):116:190 -> startUsbDriveMonitor
    1:17:void stopUsbDriveMonitor(android.content.Context):206:222 -> stopUsbDriveMonitor
com.android.rockchip.camera2.util.FileStorageUtils$1 -> com.android.rockchip.camera2.util.a:
# {"id":"sourceFile","fileName":"FileStorageUtils.java"}
    1:1:void <init>():122:122 -> <init>
    1:43:void onReceive(android.content.Context,android.content.Intent):126:168 -> onReceive
com.android.rockchip.camera2.util.FileStorageUtils$StorageListener -> com.android.rockchip.camera2.util.FileStorageUtils$StorageListener:
# {"id":"sourceFile","fileName":"FileStorageUtils.java"}
com.android.rockchip.camera2.util.HdmiService -> com.android.rockchip.camera2.util.HdmiService:
# {"id":"sourceFile","fileName":"HdmiService.java"}
    1:1:void <clinit>():92:92 -> <clinit>
    1:25:void <init>():64:88 -> <init>
    1:27:int getFrameRate():359:385 -> getFrameRate
    1:28:android.util.Size getHdmiResolution():315:342 -> getHdmiResolution
    1:1:com.android.rockchip.camera2.util.HdmiService getInstance():138:138 -> getInstance
    1:3:void init():150:152 -> init
    1:1:boolean isSignalLocked(java.lang.String):184:184 -> isSignalLocked
    1:12:java.lang.String readHdmiStatusFile():405:416 -> readHdmiStatusFile
    1:1:void setHdmiListener(com.android.rockchip.camera2.util.HdmiService$HdmiListener):120:120 -> setHdmiListener
    1:1:void stop():166:166 -> stop
com.android.rockchip.camera2.util.HdmiService$HdmiListener -> com.android.rockchip.camera2.util.HdmiService$HdmiListener:
# {"id":"sourceFile","fileName":"HdmiService.java"}
com.android.rockchip.camera2.util.HdmiService$ReadThread -> com.android.rockchip.camera2.util.b:
# {"id":"sourceFile","fileName":"HdmiService.java"}
    com.android.rockchip.camera2.util.HdmiService this$0 -> a
    1:10:void <init>(com.android.rockchip.camera2.util.HdmiService):193:202 -> <init>
    1:90:void run():207:296 -> run
com.android.rockchip.camera2.util.NetworkManager -> com.android.rockchip.camera2.util.NetworkManager:
# {"id":"sourceFile","fileName":"NetworkManager.java"}
    1:11:void <init>(android.content.Context,com.android.rockchip.camera2.util.NetworkManager$NetworkStateListener,androidx.activity.result.ActivityResultLauncher):139:149 -> <init>
    1:3:void disconnectWifi():487:489 -> disconnectWifi
    1:19:void enableAndConnectWifi():459:477 -> enableAndConnectWifi
    1:32:java.util.List getAvailableNetworkInterfaces():579:610 -> getAvailableNetworkInterfaces
    1:33:com.android.rockchip.camera2.util.NetworkManager$HotspotInfo getCurrentHotspotState():273:305 -> getCurrentHotspotState
    1:17:com.android.rockchip.camera2.util.NetworkManager$WifiConnectionInfo getCurrentWifiState():234:250 -> getCurrentWifiState
    1:8:java.lang.String getInterfaceTypeDescription(java.lang.String):620:627 -> getInterfaceTypeDescription
    9:9:java.lang.String getInterfaceTypeDescription(java.lang.String):625:625 -> getInterfaceTypeDescription
    1:5:boolean isEthernetConnected():258:262 -> isEthernetConnected
    1:9:void openHotspotSettings():330:338 -> openHotspotSettings
    1:2:void pauseHotspotMonitoring():205:206 -> pauseHotspotMonitoring
    1:4:void resumeHotspotMonitoring():214:217 -> resumeHotspotMonitoring
    1:25:void setupHotspotMonitoring():388:412 -> setupHotspotMonitoring
    1:33:void setupNetworkMonitoring():347:379 -> setupNetworkMonitoring
    1:8:void startMonitoring():163:170 -> startMonitoring
    1:20:void stopMonitoring():178:197 -> stopMonitoring
    1:3:void toggleWifi(boolean):319:321 -> toggleWifi
    1:8:void updateHotspotStatus():443:450 -> updateHotspotStatus
    1:12:void updateNetworkStatus():422:433 -> updateNetworkStatus
com.android.rockchip.camera2.util.NetworkManager$1 -> com.android.rockchip.camera2.util.c:
# {"id":"sourceFile","fileName":"NetworkManager.java"}
    com.android.rockchip.camera2.util.NetworkManager this$0 -> a
    1:1:void <init>(com.android.rockchip.camera2.util.NetworkManager):347:347 -> <init>
    1:1:void lambda$onAvailable$0():352:352 -> lambda$onAvailable$0
    1:1:void lambda$onCapabilitiesChanged$2():366:366 -> lambda$onCapabilitiesChanged$2
    1:1:void lambda$onLost$1():359:359 -> lambda$onLost$1
    1:3:void onAvailable(android.net.Network):350:352 -> onAvailable
    1:3:void onCapabilitiesChanged(android.net.Network,android.net.NetworkCapabilities):364:366 -> onCapabilitiesChanged
    1:3:void onLost(android.net.Network):357:359 -> onLost
com.android.rockchip.camera2.util.NetworkManager$2 -> com.android.rockchip.camera2.util.d:
# {"id":"sourceFile","fileName":"NetworkManager.java"}
    com.android.rockchip.camera2.util.NetworkManager this$0 -> a
    1:1:void <init>(com.android.rockchip.camera2.util.NetworkManager):388:388 -> <init>
    1:3:void run():391:393 -> run
com.android.rockchip.camera2.util.NetworkManager$3 -> com.android.rockchip.camera2.util.e:
# {"id":"sourceFile","fileName":"NetworkManager.java"}
    com.android.rockchip.camera2.util.NetworkManager this$0 -> a
    1:1:void <init>(com.android.rockchip.camera2.util.NetworkManager):402:402 -> <init>
    1:1:void onReceive(android.content.Context,android.content.Intent):405:405 -> onReceive
com.android.rockchip.camera2.util.NetworkManager$HotspotInfo -> com.android.rockchip.camera2.util.NetworkManager$HotspotInfo:
# {"id":"sourceFile","fileName":"NetworkManager.java"}
    1:3:void <init>(boolean,java.lang.String):549:551 -> <init>
    1:1:java.lang.String getInfo():569:569 -> getInfo
    1:1:boolean isEnabled():560:560 -> isEnabled
com.android.rockchip.camera2.util.NetworkManager$NetworkInterfaceInfo -> com.android.rockchip.camera2.util.NetworkManager$NetworkInterfaceInfo:
# {"id":"sourceFile","fileName":"NetworkManager.java"}
    1:4:void <init>(java.lang.String,java.lang.String,java.lang.String):650:653 -> <init>
    1:1:java.lang.String getDescription():680:680 -> getDescription
    1:1:java.lang.String getIpAddress():671:671 -> getIpAddress
    1:1:java.lang.String getName():662:662 -> getName
    1:1:java.lang.String toString():685:685 -> toString
com.android.rockchip.camera2.util.NetworkManager$NetworkStateListener -> com.android.rockchip.camera2.util.NetworkManager$NetworkStateListener:
# {"id":"sourceFile","fileName":"NetworkManager.java"}
com.android.rockchip.camera2.util.NetworkManager$WifiConnectionInfo -> com.android.rockchip.camera2.util.NetworkManager$WifiConnectionInfo:
# {"id":"sourceFile","fileName":"NetworkManager.java"}
    1:3:void <init>(boolean,java.lang.String):509:511 -> <init>
    1:1:java.lang.String getSsid():529:529 -> getSsid
    1:1:boolean isConnected():520:520 -> isConnected
com.android.rockchip.camera2.util.SMBFileUploader -> com.android.rockchip.camera2.util.SMBFileUploader:
# {"id":"sourceFile","fileName":"SMBFileUploader.java"}
    1:1:void <init>(android.content.Context):362:362 -> <init>
    2:12:void <init>(android.content.Context):354:364 -> <init>
    1:1:com.android.rockchip.camera2.util.SMBFileUploader$SMBConfig getConnectionParams():441:441 -> getConnectionParams
    1:6:void getRemoteDirectories(com.android.rockchip.camera2.util.SMBFileUploader$DirectoryListListener):482:487 -> getRemoteDirectories
    7:7:void getRemoteDirectories(com.android.rockchip.camera2.util.SMBFileUploader$DirectoryListListener):483:483 -> getRemoteDirectories
    1:1:boolean isEnabled():450:450 -> isEnabled
    1:9:void loadSettings():371:379 -> loadSettings
    1:11:void saveSettings():386:396 -> saveSettings
    1:7:void setConnectionParams(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,boolean):411:417 -> setConnectionParams
    8:14:void setConnectionParams(com.android.rockchip.camera2.util.SMBFileUploader$SMBConfig):426:432 -> setConnectionParams
    1:2:void setEnabled(boolean):457:458 -> setEnabled
    1:8:void testConnection(com.android.rockchip.camera2.util.SMBFileUploader$UploadListener):467:474 -> testConnection
    1:7:void uploadFile(java.lang.String,com.android.rockchip.camera2.util.SMBFileUploader$UploadListener):497:503 -> uploadFile
    8:14:void uploadFile(java.lang.String,java.lang.String,com.android.rockchip.camera2.util.SMBFileUploader$UploadListener):514:520 -> uploadFile
com.android.rockchip.camera2.util.SMBFileUploader$DirectoryListListener -> com.android.rockchip.camera2.util.SMBFileUploader$DirectoryListListener:
# {"id":"sourceFile","fileName":"SMBFileUploader.java"}
com.android.rockchip.camera2.util.SMBFileUploader$ListDirectoriesTask -> com.android.rockchip.camera2.util.f:
# {"id":"sourceFile","fileName":"SMBFileUploader.java"}
    com.android.rockchip.camera2.util.SMBFileUploader this$0 -> a
    1:1:void <init>(com.android.rockchip.camera2.util.SMBFileUploader,com.android.rockchip.camera2.util.SMBFileUploader$DirectoryListListener):605:605 -> <init>
    2:5:void <init>(com.android.rockchip.camera2.util.SMBFileUploader,com.android.rockchip.camera2.util.SMBFileUploader$DirectoryListListener):603:606 -> <init>
    1:1:java.lang.Object doInBackground(java.lang.Object[]):601:601 -> doInBackground
    2:69:java.util.List doInBackground(java.lang.Void[]):611:678 -> doInBackground
    2:69:java.lang.Object doInBackground(java.lang.Object[]):601 -> doInBackground
    70:70:java.util.List doInBackground(java.lang.Void[]):675:675 -> doInBackground
    70:70:java.lang.Object doInBackground(java.lang.Object[]):601 -> doInBackground
    71:71:java.util.List doInBackground(java.lang.Void[]):672:672 -> doInBackground
    71:71:java.lang.Object doInBackground(java.lang.Object[]):601 -> doInBackground
    1:1:void onPostExecute(java.lang.Object):601:601 -> onPostExecute
    2:6:void onPostExecute(java.util.List):687:691 -> onPostExecute
    2:6:void onPostExecute(java.lang.Object):601 -> onPostExecute
com.android.rockchip.camera2.util.SMBFileUploader$SMBConfig -> com.android.rockchip.camera2.util.SMBFileUploader$SMBConfig:
# {"id":"sourceFile","fileName":"SMBFileUploader.java"}
    1:7:void <init>(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,boolean):330:336 -> <init>
    1:1:java.lang.String getRemotePath():342:342 -> getRemotePath
    1:1:java.lang.String getServerIp():339:339 -> getServerIp
    1:1:java.lang.String getShareName():341:341 -> getShareName
    1:1:java.lang.String getUsername():340:340 -> getUsername
    1:1:boolean isEnabled():343:343 -> isEnabled
com.android.rockchip.camera2.util.SMBFileUploader$TestConnectionTask -> com.android.rockchip.camera2.util.g:
# {"id":"sourceFile","fileName":"SMBFileUploader.java"}
    com.android.rockchip.camera2.util.SMBFileUploader this$0 -> a
    1:1:void <init>(com.android.rockchip.camera2.util.SMBFileUploader,com.android.rockchip.camera2.util.SMBFileUploader$UploadListener):532:532 -> <init>
    2:5:void <init>(com.android.rockchip.camera2.util.SMBFileUploader,com.android.rockchip.camera2.util.SMBFileUploader$UploadListener):530:533 -> <init>
    1:1:java.lang.Object doInBackground(java.lang.Object[]):528:528 -> doInBackground
    2:41:java.lang.Boolean doInBackground(java.lang.Void[]):540:579 -> doInBackground
    2:41:java.lang.Object doInBackground(java.lang.Object[]):528 -> doInBackground
    42:42:java.lang.Boolean doInBackground(java.lang.Void[]):576:576 -> doInBackground
    42:42:java.lang.Object doInBackground(java.lang.Object[]):528 -> doInBackground
    43:53:java.lang.Boolean doInBackground(java.lang.Void[]):573:583 -> doInBackground
    43:53:java.lang.Object doInBackground(java.lang.Object[]):528 -> doInBackground
    1:1:void onPostExecute(java.lang.Object):528:528 -> onPostExecute
    2:6:void onPostExecute(java.lang.Boolean):588:592 -> onPostExecute
    2:6:void onPostExecute(java.lang.Object):528 -> onPostExecute
com.android.rockchip.camera2.util.SMBFileUploader$UploadListener -> com.android.rockchip.camera2.util.SMBFileUploader$UploadListener:
# {"id":"sourceFile","fileName":"SMBFileUploader.java"}
com.android.rockchip.camera2.util.SMBFileUploader$UploadTask -> com.android.rockchip.camera2.util.h:
# {"id":"sourceFile","fileName":"SMBFileUploader.java"}
    com.android.rockchip.camera2.util.SMBFileUploader this$0 -> a
    1:1:void <init>(com.android.rockchip.camera2.util.SMBFileUploader,java.lang.String,com.android.rockchip.camera2.util.SMBFileUploader$UploadListener):707:707 -> <init>
    2:8:void <init>(com.android.rockchip.camera2.util.SMBFileUploader,java.lang.String,com.android.rockchip.camera2.util.SMBFileUploader$UploadListener):704:710 -> <init>
    9:9:void <init>(com.android.rockchip.camera2.util.SMBFileUploader,java.lang.String,java.lang.String,com.android.rockchip.camera2.util.SMBFileUploader$UploadListener):713:713 -> <init>
    10:22:void <init>(com.android.rockchip.camera2.util.SMBFileUploader,java.lang.String,java.lang.String,com.android.rockchip.camera2.util.SMBFileUploader$UploadListener):704:716 -> <init>
    1:1:java.lang.Object doInBackground(java.lang.Object[]):700:700 -> doInBackground
    2:55:java.lang.Boolean doInBackground(java.lang.Void[]):723:776 -> doInBackground
    2:55:java.lang.Object doInBackground(java.lang.Object[]):700 -> doInBackground
    56:56:java.lang.Boolean doInBackground(java.lang.Void[]):773:773 -> doInBackground
    56:56:java.lang.Object doInBackground(java.lang.Object[]):700 -> doInBackground
    57:67:java.lang.Boolean doInBackground(java.lang.Void[]):770:780 -> doInBackground
    57:67:java.lang.Object doInBackground(java.lang.Object[]):700 -> doInBackground
    1:1:void onPostExecute(java.lang.Object):700:700 -> onPostExecute
    2:6:void onPostExecute(java.lang.Boolean):785:789 -> onPostExecute
    2:6:void onPostExecute(java.lang.Object):700 -> onPostExecute
com.android.rockchip.camera2.util.TouptekIspParam -> com.android.rockchip.camera2.util.TouptekIspParam:
# {"id":"sourceFile","fileName":"TouptekIspParam.java"}
    1:1:com.android.rockchip.camera2.util.TouptekIspParam[] $values():57:57 -> $values
    1:97:void <clinit>():60:156 -> <clinit>
    98:228:void <clinit>():57:187 -> <clinit>
    1:2:void <init>(java.lang.String,int,int):194:195 -> <init>
    1:2:void addOnDataChangedListener(com.android.rockchip.camera2.util.TouptekIspParam$OnDataChangedListener):898:899 -> addOnDataChangedListener
    1:3:com.android.rockchip.camera2.util.TouptekIspParam fromInt(int):801:803 -> fromInt
    1:1:java.util.Map getAllCurrentValues():1314:1314 -> getAllCurrentValues
    1:4:java.util.List getAllParamInfo():712:715 -> getAllParamInfo
    1:1:long getCurrentLongValue(com.android.rockchip.camera2.util.TouptekIspParam):1304:1304 -> getCurrentLongValue
    1:1:int getCurrentValue(com.android.rockchip.camera2.util.TouptekIspParam):1292:1292 -> getCurrentValue
    1:2:int getDefaultValue(com.android.rockchip.camera2.util.TouptekIspParam):533:534 -> getDefaultValue
    1:2:boolean getIsDisableValue(com.android.rockchip.camera2.util.TouptekIspParam):620:621 -> getIsDisableValue
    1:2:int getMaxValue(com.android.rockchip.camera2.util.TouptekIspParam):448:449 -> getMaxValue
    1:2:int getMinValue(com.android.rockchip.camera2.util.TouptekIspParam):361:362 -> getMinValue
    1:4:com.android.rockchip.camera2.util.TouptekIspParam getParamByIndex(int):1328:1331 -> getParamByIndex
    1:1:int getParamId():205:205 -> getParamId
    1:15:com.android.rockchip.camera2.util.TouptekIspParam$ParamData getParamInfo(com.android.rockchip.camera2.util.TouptekIspParam):687:701 -> getParamInfo
    1:7:void handleReceivedData(com.android.rockchip.camera2.util.TouptekIspParam,long,boolean):1274:1280 -> handleReceivedData
    1:21:void init(android.content.Context):822:842 -> init
    1:1:boolean isRangeReceived():280:280 -> isRangeReceived
    1:1:boolean isSerialConnected():856:856 -> isSerialConnected
    1:9:void notifyDataChanged(com.android.rockchip.camera2.util.TouptekIspParam,int):928:936 -> notifyDataChanged
    1:6:void notifyLongDataChanged(com.android.rockchip.camera2.util.TouptekIspParam,long):954:959 -> notifyLongDataChanged
    1:5:void release():881:885 -> release
    1:1:void removeOnDataChangedListener(com.android.rockchip.camera2.util.TouptekIspParam$OnDataChangedListener):913:913 -> removeOnDataChangedListener
    1:1:void requestAllParamRanges():1182:1182 -> requestAllParamRanges
    2:45:void requestAllParamRanges(boolean):1196:1239 -> requestAllParamRanges
    1:89:int saveAllDefaultValuesToLocal(boolean):981:1069 -> saveAllDefaultValuesToLocal
    1:3:void saveToLocal(com.android.rockchip.camera2.util.TouptekIspParam,int):1131:1133 -> saveToLocal
    4:6:void saveToLocal(com.android.rockchip.camera2.util.TouptekIspParam,long):1147:1149 -> saveToLocal
    1:7:void sendToDevice(com.android.rockchip.camera2.util.TouptekIspParam,int):1163:1169 -> sendToDevice
    8:8:void sendToDevice(com.android.rockchip.camera2.util.TouptekIspParam,int,int):1256:1256 -> sendToDevice
    1:1:void setOnSerialStateChangedListener(com.android.rockchip.camera2.util.TouptekIspParam$OnSerialStateChangedListener):869:869 -> setOnSerialStateChangedListener
    1:1:void setParamDefault(com.android.rockchip.camera2.util.TouptekIspParam,int):235:235 -> setParamDefault
    1:1:void setParamDisabled(com.android.rockchip.camera2.util.TouptekIspParam,boolean):246:246 -> setParamDisabled
    1:1:void setParamMaxValue(com.android.rockchip.camera2.util.TouptekIspParam,int):225:225 -> setParamMaxValue
    1:1:void setParamMinValue(com.android.rockchip.camera2.util.TouptekIspParam,int):215:215 -> setParamMinValue
    1:4:void setParamRange(com.android.rockchip.camera2.util.TouptekIspParam,boolean,int,int,int):259:262 -> setParamRange
    1:1:void setParamsRangeReceived(boolean):271:271 -> setParamsRangeReceived
    1:52:void syncAllCurrentValuesToDevice():736:787 -> syncAllCurrentValuesToDevice
    1:9:void updateParam(com.android.rockchip.camera2.util.TouptekIspParam,int):1086:1094 -> updateParam
    10:15:void updateParam(com.android.rockchip.camera2.util.TouptekIspParam,int,int):1111:1116 -> updateParam
    1:1:com.android.rockchip.camera2.util.TouptekIspParam valueOf(java.lang.String):57:57 -> valueOf
    1:1:com.android.rockchip.camera2.util.TouptekIspParam[] values():57:57 -> values
com.android.rockchip.camera2.util.TouptekIspParam$1 -> com.android.rockchip.camera2.util.TouptekIspParam$1:
# {"id":"sourceFile","fileName":"TouptekIspParam.java"}
    1:1:void <init>():831:831 -> <init>
    1:3:void onDeviceStateChanged(boolean):836:838 -> onDeviceStateChanged
com.android.rockchip.camera2.util.TouptekIspParam$OnDataChangedListener -> com.android.rockchip.camera2.util.TouptekIspParam$OnDataChangedListener:
# {"id":"sourceFile","fileName":"TouptekIspParam.java"}
com.android.rockchip.camera2.util.TouptekIspParam$OnSerialStateChangedListener -> com.android.rockchip.camera2.util.TouptekIspParam$OnSerialStateChangedListener:
# {"id":"sourceFile","fileName":"TouptekIspParam.java"}
com.android.rockchip.camera2.util.TouptekIspParam$ParamData -> com.android.rockchip.camera2.util.TouptekIspParam$ParamData:
# {"id":"sourceFile","fileName":"TouptekIspParam.java"}
    1:6:void <init>(int,int,int,int,boolean):327:332 -> <init>
    1:2:java.lang.String toString():338:339 -> toString
    3:3:java.lang.String toString():338:338 -> toString
com.android.rockchip.camera2.util.TransformUtils -> com.android.rockchip.camera2.util.TransformUtils:
# {"id":"sourceFile","fileName":"TransformUtils.java"}
    1:1:void <clinit>():20:20 -> <clinit>
    1:1:void <init>():16:16 -> <init>
    1:46:void applyPan(android.view.TextureView,com.android.rockchip.camera2.view.TpRoiView,float,float):128:173 -> applyPan
    47:47:void applyPan(android.view.TextureView,float,float):227:227 -> applyPan
    48:96:void applyPan(android.widget.ImageView,android.graphics.Matrix,float,float):334:382 -> applyPan
    1:71:void applyZoom(android.view.TextureView,com.android.rockchip.camera2.view.TpRoiView,float,float,float):39:109 -> applyZoom
    72:72:void applyZoom(android.view.TextureView,float,float,float):216:216 -> applyZoom
    73:106:void applyZoom(android.widget.ImageView,android.graphics.Matrix,float,float,float):246:279 -> applyZoom
    1:20:float calculateBoundaryCorrection(android.graphics.RectF,android.graphics.RectF,boolean):292:311 -> calculateBoundaryCorrection
    1:13:float getCurrentScale(android.view.TextureView):398:410 -> getCurrentScale
    1:1:android.graphics.Matrix getCurrentTransformMatrix():182:182 -> getCurrentTransformMatrix
    1:34:android.graphics.Matrix resetImageViewTransform(android.widget.ImageView):422:455 -> resetImageViewTransform
    1:9:void resetTransform(android.view.TextureView,com.android.rockchip.camera2.view.TpRoiView):196:204 -> resetTransform
com.android.rockchip.camera2.util.touptek_serial_rk -> com.android.rockchip.camera2.util.touptek_serial_rk:
# {"id":"sourceFile","fileName":"touptek_serial_rk.java"}
    1:1:void <clinit>():26:26 -> <clinit>
    1:1:void <init>():17:17 -> <init>
    1:1:void close():259:259 -> close
    1:1:boolean initializeSerial(int):131:131 -> initializeSerial
    1:2:void lambda$onDeviceStateChanged$0():148:149 -> lambda$onDeviceStateChanged$0
    1:14:void onDeviceStateChanged(boolean):142:155 -> onDeviceStateChanged
    1:44:void onSerialDataReceived(int[]):188:231 -> onSerialDataReceived
    45:46:void onSerialDataReceived(int[]):226:227 -> onSerialDataReceived
    47:48:void onSerialDataReceived(int[]):222:223 -> onSerialDataReceived
    49:50:void onSerialDataReceived(int[]):218:219 -> onSerialDataReceived
    51:84:void onSerialDataReceived(int[]):212:245 -> onSerialDataReceived
    85:85:void onSerialDataReceived(int[]):240:240 -> onSerialDataReceived
    1:16:void sendCommandToSerial(int,int,int):103:118 -> sendCommandToSerial
    1:1:void setDeviceStateCallback(com.android.rockchip.camera2.util.touptek_serial_rk$DeviceStateCallback):172:172 -> setDeviceStateCallback
com.android.rockchip.camera2.util.touptek_serial_rk$DeviceStateCallback -> com.android.rockchip.camera2.util.touptek_serial_rk$DeviceStateCallback:
# {"id":"sourceFile","fileName":"touptek_serial_rk.java"}
com.android.rockchip.camera2.video.ImageDecoder -> com.android.rockchip.camera2.video.ImageDecoder:
# {"id":"sourceFile","fileName":"ImageDecoder.java"}
    1:23:void <clinit>():36:58 -> <clinit>
    1:1:void <init>():32:32 -> <init>
    1:8:long calculateCacheSize():265:272 -> calculateCacheSize
    1:11:int calculateInSampleSize(android.graphics.BitmapFactory$Options,int,int):607:617 -> calculateInSampleSize
    1:22:void cleanupCache():237:258 -> cleanupCache
    1:6:void cleanupCacheIfNeeded():224:229 -> cleanupCacheIfNeeded
    1:1:void clearCache():281:281 -> clearCache
    1:19:android.graphics.Bitmap decodeSampledBitmapFromFile(java.lang.String,int,int):571:589 -> decodeSampledBitmapFromFile
    1:37:android.graphics.Bitmap extractVideoThumbnail(java.lang.String):671:707 -> extractVideoThumbnail
    38:42:android.graphics.Bitmap extractVideoThumbnail(java.lang.String):703:707 -> extractVideoThumbnail
    43:47:android.graphics.Bitmap extractVideoThumbnail(java.lang.String):707:711 -> extractVideoThumbnail
    1:16:java.lang.String generateCacheFileName(java.lang.String,int,int):105:120 -> generateCacheFileName
    1:33:android.graphics.Bitmap getBitmapFromCache(android.content.Context,java.lang.String,int,int):128:160 -> getBitmapFromCache
    1:15:java.lang.String getCacheStats():309:323 -> getCacheStats
    16:16:java.lang.String getCacheStats():319:319 -> getCacheStats
    1:10:java.lang.String getFileExtension(java.lang.String):637:646 -> getFileExtension
    11:11:java.lang.String getFileExtension(java.lang.String):638:638 -> getFileExtension
    1:14:void initCache(android.content.Context):85:98 -> initCache
    1:5:boolean isFileModified(java.lang.String,java.io.File):203:207 -> isFileModified
    1:19:void lambda$clearCache$1():282:300 -> lambda$clearCache$1
    1:1:void lambda$loadImage$2(android.widget.ImageView,android.graphics.Bitmap):370:370 -> lambda$loadImage$2
    1:12:void lambda$loadImageAsync$3(int[],android.widget.ImageView):440:451 -> lambda$loadImageAsync$3
    13:13:void lambda$loadImageAsync$3(int[],android.widget.ImageView):451:451 -> lambda$loadImageAsync$3
    1:4:void lambda$loadImageAsync$4(java.lang.String,android.widget.ImageView,android.graphics.Bitmap,long):484:487 -> lambda$loadImageAsync$4
    1:8:void lambda$loadImageAsync$5(java.lang.String,android.widget.ImageView,android.graphics.Bitmap,long):525:532 -> lambda$loadImageAsync$5
    1:127:void lambda$loadImageAsync$6(long,java.lang.String,android.widget.ImageView):422:548 -> lambda$loadImageAsync$6
    128:245:void lambda$loadImageAsync$6(long,java.lang.String,android.widget.ImageView):431:548 -> lambda$loadImageAsync$6
    246:330:void lambda$loadImageAsync$6(long,java.lang.String,android.widget.ImageView):459:543 -> lambda$loadImageAsync$6
    331:339:void lambda$loadImageAsync$6(long,java.lang.String,android.widget.ImageView):541:549 -> lambda$loadImageAsync$6
    1:18:void lambda$putBitmapToCache$0(java.lang.String,int,int,android.graphics.Bitmap):175:192 -> lambda$putBitmapToCache$0
    19:19:void lambda$putBitmapToCache$0(java.lang.String,int,int,android.graphics.Bitmap):179:179 -> lambda$putBitmapToCache$0
    1:32:boolean loadImage(java.lang.String,android.widget.ImageView):345:376 -> loadImage
    33:66:boolean loadImage(java.lang.String,android.widget.ImageView):347:380 -> loadImage
    1:150:void loadImageAsync(java.lang.String,android.widget.ImageView):405:554 -> loadImageAsync
    1:6:void putBitmapToCache(android.content.Context,java.lang.String,int,int,android.graphics.Bitmap):168:173 -> putBitmapToCache
    1:3:android.graphics.Bitmap rotateBitmap(android.graphics.Bitmap,float):659:661 -> rotateBitmap
    1:3:void updateFileInfo(java.lang.String):214:216 -> updateFileInfo
com.android.rockchip.camera2.video.ImageDecoder$FileInfo -> com.android.rockchip.camera2.video.a:
# {"id":"sourceFile","fileName":"ImageDecoder.java"}
    1:1:void <init>(long,long):73:73 -> <init>
      # {"id":"com.android.tools.r8.residualsignature","signature":"()V"}
com.android.rockchip.camera2.video.TpCameraManager -> com.android.rockchip.camera2.video.TpCameraManager:
# {"id":"sourceFile","fileName":"TpCameraManager.java"}
    1:5:void <init>(com.android.rockchip.camera2.video.TpCameraManager$Builder):52:56 -> <init>
    1:1:com.android.rockchip.camera2.video.TpCameraManager$Builder builder(android.content.Context):69:69 -> builder
    1:13:void configCameraOutputs(android.hardware.camera2.CameraDevice,android.view.Surface,android.view.Surface):201:213 -> configCameraOutputs
    14:14:void configCameraOutputs(android.hardware.camera2.CameraDevice,android.view.Surface,android.view.Surface):212:212 -> configCameraOutputs
    1:7:void openCamera():146:152 -> openCamera
    1:22:void releaseCamera():322:343 -> releaseCamera
    1:10:boolean resetHdmiRxViaScript():258:267 -> resetHdmiRxViaScript
    1:3:void startBackgroundThread():284:286 -> startBackgroundThread
    1:8:void stopBackgroundThread():297:304 -> stopBackgroundThread
com.android.rockchip.camera2.video.TpCameraManager$1 -> com.android.rockchip.camera2.video.b:
# {"id":"sourceFile","fileName":"TpCameraManager.java"}
    com.android.rockchip.camera2.video.TpCameraManager this$0 -> a
    1:1:void <init>(com.android.rockchip.camera2.video.TpCameraManager):152:152 -> <init>
    1:4:void onDisconnected(android.hardware.camera2.CameraDevice):162:165 -> onDisconnected
    1:4:void onError(android.hardware.camera2.CameraDevice,int):170:173 -> onError
    1:2:void onOpened(android.hardware.camera2.CameraDevice):155:156 -> onOpened
com.android.rockchip.camera2.video.TpCameraManager$2 -> com.android.rockchip.camera2.video.c:
# {"id":"sourceFile","fileName":"TpCameraManager.java"}
    android.hardware.camera2.CaptureRequest$Builder val$builder -> a
    com.android.rockchip.camera2.video.TpCameraManager this$0 -> b
    1:1:void <init>(com.android.rockchip.camera2.video.TpCameraManager,android.hardware.camera2.CaptureRequest$Builder):215:215 -> <init>
    1:1:void onConfigureFailed(android.hardware.camera2.CameraCaptureSession):236:236 -> onConfigureFailed
    1:5:void onConfigured(android.hardware.camera2.CameraCaptureSession):219:223 -> onConfigured
com.android.rockchip.camera2.video.TpCameraManager$Builder -> com.android.rockchip.camera2.video.TpCameraManager$Builder:
# {"id":"sourceFile","fileName":"TpCameraManager.java"}
    1:2:void <init>(android.content.Context):86:87 -> <init>
    1:1:com.android.rockchip.camera2.video.TpCameraManager build():132:132 -> build
    1:1:com.android.rockchip.camera2.video.TpCameraManager$Builder onCameraDisconnected(java.util.function.Consumer):108:108 -> onCameraDisconnected
    1:1:com.android.rockchip.camera2.video.TpCameraManager$Builder onCameraError(java.util.function.BiConsumer):119:119 -> onCameraError
    1:1:com.android.rockchip.camera2.video.TpCameraManager$Builder onCameraOpened(java.util.function.Consumer):97:97 -> onCameraOpened
com.android.rockchip.camera2.video.TpCaptureImage -> com.android.rockchip.camera2.video.TpCaptureImage:
# {"id":"sourceFile","fileName":"TpCaptureImage.java"}
    1:1:void <init>(com.android.rockchip.camera2.video.TpCaptureImage$Builder):170:170 -> <init>
    2:61:void <init>(com.android.rockchip.camera2.video.TpCaptureImage$Builder):116:175 -> <init>
    62:71:void <init>(com.android.rockchip.camera2.video.TpCaptureImage$Builder):173:182 -> <init>
    1:1:com.android.rockchip.camera2.video.TpCaptureImage$Builder builder(android.util.Size):196:196 -> builder
    1:15:byte[] convertYUV420ToNV21(java.nio.ByteBuffer,int,int):626:640 -> convertYUV420ToNV21
    1:19:android.graphics.Bitmap convertYUVToBitmap(java.nio.ByteBuffer,android.util.Size,android.util.Size):553:571 -> convertYUVToBitmap
    1:14:java.nio.ByteBuffer copyImageToBuffer(android.media.Image):591:604 -> copyImageToBuffer
    1:11:int detectFormatFromPath(java.lang.String):421:431 -> detectFormatFromPath
    1:1:android.media.ImageReader getImageReader():377:377 -> getImageReader
    1:1:byte[] intToBytes(int):954:954 -> intToBytes
    1:19:void lambda$onImageAvailable$0(java.nio.ByteBuffer,android.util.Size):506:524 -> lambda$onImageAvailable$0
    1:1:void lambda$saveImageAsync$1(com.android.rockchip.camera2.video.TpCaptureImage$ImageCaptureListener,java.lang.String):866:866 -> lambda$saveImageAsync$1
    1:1:void lambda$saveImageAsync$2(com.android.rockchip.camera2.video.TpCaptureImage$ImageCaptureListener,java.lang.String):872:872 -> lambda$saveImageAsync$2
    1:1:void lambda$saveImageAsync$3(com.android.rockchip.camera2.video.TpCaptureImage$ImageCaptureListener,java.lang.String):887:887 -> lambda$saveImageAsync$3
    1:1:void lambda$saveImageAsync$4(com.android.rockchip.camera2.video.TpCaptureImage$ImageCaptureListener):892:892 -> lambda$saveImageAsync$4
    1:1:void lambda$saveImageAsync$5(int,android.graphics.Bitmap,java.lang.String,com.android.rockchip.camera2.video.TpCaptureImage$ImageCaptureListener):877:877 -> lambda$saveImageAsync$5
    2:4:void lambda$saveImageAsync$5(int,android.graphics.Bitmap,java.lang.String,com.android.rockchip.camera2.video.TpCaptureImage$ImageCaptureListener):870:872 -> lambda$saveImageAsync$5
    5:7:void lambda$saveImageAsync$5(int,android.graphics.Bitmap,java.lang.String,com.android.rockchip.camera2.video.TpCaptureImage$ImageCaptureListener):864:866 -> lambda$saveImageAsync$5
    8:34:void lambda$saveImageAsync$5(int,android.graphics.Bitmap,java.lang.String,com.android.rockchip.camera2.video.TpCaptureImage$ImageCaptureListener):861:887 -> lambda$saveImageAsync$5
    35:46:void lambda$saveImageAsync$5(int,android.graphics.Bitmap,java.lang.String,com.android.rockchip.camera2.video.TpCaptureImage$ImageCaptureListener):881:892 -> lambda$saveImageAsync$5
    1:48:void onImageAvailable(android.media.ImageReader):484:531 -> onImageAvailable
    1:6:void release():972:977 -> release
    1:4:void requestCapture(android.util.Size,java.lang.String):408:411 -> requestCapture
    5:18:void requestCapture(android.util.Size,java.lang.String,int):454:467 -> requestCapture
    1:50:void saveBitmap(android.graphics.Bitmap,java.lang.String):661:710 -> saveBitmap
    51:51:void saveBitmap(android.graphics.Bitmap,java.lang.String):669:669 -> saveBitmap
    1:40:void saveImageAsync(android.graphics.Bitmap,java.lang.String,int,com.android.rockchip.camera2.video.TpCaptureImage$ImageCaptureListener):856:895 -> saveImageAsync
    1:31:void saveImageWithFormat(android.graphics.Bitmap,java.lang.String,int):917:947 -> saveImageWithFormat
    1:92:void saveTiff(android.graphics.Bitmap,java.lang.String):727:818 -> saveTiff
    93:93:void saveTiff(android.graphics.Bitmap,java.lang.String):732:732 -> saveTiff
    1:1:void setCaptureCallback(com.android.rockchip.camera2.video.TpCaptureImage$ImageCaptureListener):391:391 -> setCaptureCallback
    1:18:void writeTag(java.io.FileOutputStream,int,int,int,int):826:843 -> writeTag
com.android.rockchip.camera2.video.TpCaptureImage$1 -> com.android.rockchip.camera2.video.TpCaptureImage$1:
# {"id":"sourceFile","fileName":"TpCaptureImage.java"}
    1:1:void <init>(com.android.rockchip.camera2.video.TpCaptureImage):921:921 -> <init>
    1:6:void onError(java.lang.String):937:942 -> onError
    1:6:void onImageSaved(java.lang.String):925:930 -> onImageSaved
com.android.rockchip.camera2.video.TpCaptureImage$Builder -> com.android.rockchip.camera2.video.TpCaptureImage$Builder:
# {"id":"sourceFile","fileName":"TpCaptureImage.java"}
    1:1:void <init>(android.util.Size):228:228 -> <init>
    2:29:void <init>(android.util.Size):208:235 -> <init>
    30:30:void <init>(android.util.Size):230:230 -> <init>
    1:19:com.android.rockchip.camera2.video.TpCaptureImage build():343:361 -> build
    1:1:com.android.rockchip.camera2.video.TpCaptureImage$Builder onError(java.util.function.Consumer):279:279 -> onError
    1:1:com.android.rockchip.camera2.video.TpCaptureImage$Builder onImageSaved(java.util.function.Consumer):264:264 -> onImageSaved
    1:1:com.android.rockchip.camera2.video.TpCaptureImage$Builder setCaptureCallback(com.android.rockchip.camera2.video.TpCaptureImage$ImageCaptureListener):249:249 -> setCaptureCallback
    1:1:com.android.rockchip.camera2.video.TpCaptureImage$Builder setImageFormat(int):294:294 -> setImageFormat
    1:1:com.android.rockchip.camera2.video.TpCaptureImage$Builder setImageOutputFormat(int):327:327 -> setImageOutputFormat
    1:1:com.android.rockchip.camera2.video.TpCaptureImage$Builder setMaxImages(int):309:309 -> setMaxImages
com.android.rockchip.camera2.video.TpCaptureImage$Builder$1 -> com.android.rockchip.camera2.video.TpCaptureImage$Builder$1:
# {"id":"sourceFile","fileName":"TpCaptureImage.java"}
    1:1:void <init>(com.android.rockchip.camera2.video.TpCaptureImage$Builder):344:344 -> <init>
    1:2:void onError(java.lang.String):354:355 -> onError
    1:2:void onImageSaved(java.lang.String):347:348 -> onImageSaved
com.android.rockchip.camera2.video.TpCaptureImage$ImageCaptureListener -> com.android.rockchip.camera2.video.TpCaptureImage$ImageCaptureListener:
# {"id":"sourceFile","fileName":"TpCaptureImage.java"}
com.android.rockchip.camera2.video.TpImageLoader -> com.android.rockchip.camera2.video.TpImageLoader:
# {"id":"sourceFile","fileName":"TpImageLoader.java"}
    1:38:void <clinit>():66:103 -> <clinit>
    1:1:void <init>():59:59 -> <init>
    1:11:int calculateInSampleSize(android.graphics.BitmapFactory$Options,int,int):1178:1188 -> calculateInSampleSize
    1:48:void checkAndCleanDiskCache():958:1005 -> checkAndCleanDiskCache
    1:4:void clearCache():618:621 -> clearCache
    1:19:android.graphics.Bitmap decodeSampledBitmapFromFile(java.lang.String,int,int):1096:1114 -> decodeSampledBitmapFromFile
    20:37:android.graphics.Bitmap decodeSampledBitmapFromFile(java.lang.String,int,int):1101:1118 -> decodeSampledBitmapFromFile
    1:27:android.graphics.Bitmap decodeTiffWithSampling(java.lang.String,int,int):1134:1160 -> decodeTiffWithSampling
    1:14:java.lang.String generateCacheKey(java.lang.String,int,int):561:574 -> generateCacheKey
    1:42:java.lang.String getCacheStats():681:722 -> getCacheStats
    43:49:java.lang.String getCacheStats():720:726 -> getCacheStats
    50:56:java.lang.String getCacheStats():723:729 -> getCacheStats
    57:57:java.lang.String getCacheStats():685:685 -> getCacheStats
    1:12:java.lang.String getFileExtension(java.lang.String):1221:1232 -> getFileExtension
    13:13:java.lang.String getFileExtension(java.lang.String):1223:1223 -> getFileExtension
    1:28:int[] getImageViewDimensions(android.widget.ImageView):760:787 -> getImageViewDimensions
    1:43:void initializeDiskCache(android.content.Context):499:541 -> initializeDiskCache
    1:4:boolean isVideoFile(java.lang.String):590:593 -> isVideoFile
    1:1:int lambda$checkAndCleanDiskCache$8(java.io.File,java.io.File):990:990 -> lambda$checkAndCleanDiskCache$8
    1:23:void lambda$clearCache$6():625:647 -> lambda$clearCache$6
    1:3:void lambda$loadFullImage$3(android.graphics.Bitmap):419:421 -> lambda$loadFullImage$3
    1:10:void lambda$loadFullImage$4(java.lang.String,android.widget.ImageView,android.graphics.Bitmap,java.lang.String):409:418 -> lambda$loadFullImage$4
    1:47:void lambda$loadFullImage$5(java.lang.String,java.lang.String,android.widget.ImageView):382:428 -> lambda$loadFullImage$5
    1:9:void lambda$loadThumbnail$0(android.graphics.Bitmap):268:276 -> lambda$loadThumbnail$0
    1:12:void lambda$loadThumbnail$1(java.lang.String,android.widget.ImageView,android.graphics.Bitmap,java.lang.String):253:264 -> lambda$loadThumbnail$1
    1:101:void lambda$loadThumbnail$2(java.lang.String,java.lang.String,int[],android.content.Context,android.widget.ImageView):194:294 -> lambda$loadThumbnail$2
    102:196:void lambda$loadThumbnail$2(java.lang.String,java.lang.String,int[],android.content.Context,android.widget.ImageView):200:294 -> lambda$loadThumbnail$2
    197:281:void lambda$loadThumbnail$2(java.lang.String,java.lang.String,int[],android.content.Context,android.widget.ImageView):210:294 -> lambda$loadThumbnail$2
    282:326:void lambda$loadThumbnail$2(java.lang.String,java.lang.String,int[],android.content.Context,android.widget.ImageView):250:294 -> lambda$loadThumbnail$2
    327:336:void lambda$loadThumbnail$2(java.lang.String,java.lang.String,int[],android.content.Context,android.widget.ImageView):286:295 -> lambda$loadThumbnail$2
    1:48:void lambda$saveToDiskCache$7(java.lang.String,android.graphics.Bitmap):893:940 -> lambda$saveToDiskCache$7
    49:52:void lambda$saveToDiskCache$7(java.lang.String,android.graphics.Bitmap):940:943 -> lambda$saveToDiskCache$7
    1:28:android.graphics.Bitmap loadFromDiskCache(java.lang.String):817:844 -> loadFromDiskCache
    1:36:boolean loadFullImage(java.lang.String,android.widget.ImageView):340:375 -> loadFullImage
    37:119:boolean loadFullImage(java.lang.String,android.widget.ImageView):352:434 -> loadFullImage
    1:144:void loadThumbnail(java.lang.String,android.widget.ImageView):156:299 -> loadThumbnail
    1:11:android.graphics.Bitmap loadVideoThumbnailWithGlide(android.content.Context,java.lang.String,int,int):1047:1057 -> loadVideoThumbnailWithGlide
    1:3:android.graphics.Bitmap rotateBitmap(android.graphics.Bitmap,float):472:474 -> rotateBitmap
    1:7:void saveToDiskCache(java.lang.String,android.graphics.Bitmap):878:884 -> saveToDiskCache
com.android.rockchip.camera2.video.TpImageLoader$1 -> com.android.rockchip.camera2.video.d:
# {"id":"sourceFile","fileName":"TpImageLoader.java"}
    1:1:void <init>(int):83:83 -> <init>
      # {"id":"com.android.tools.r8.residualsignature","signature":"()V"}
    1:1:int sizeOf(java.lang.Object,java.lang.Object):83:83 -> sizeOf
    2:2:int sizeOf(java.lang.String,android.graphics.Bitmap):87:87 -> sizeOf
    2:2:int sizeOf(java.lang.Object,java.lang.Object):83 -> sizeOf
com.android.rockchip.camera2.video.TpVideoConfig -> com.android.rockchip.camera2.video.TpVideoConfig:
# {"id":"sourceFile","fileName":"TpVideoConfig.java"}
    1:8:void <init>(com.android.rockchip.camera2.video.TpVideoConfig$Builder):28:35 -> <init>
    1:4:com.android.rockchip.camera2.video.TpVideoConfig createDefault1080P():43:46 -> createDefault1080P
    1:4:com.android.rockchip.camera2.video.TpVideoConfig createDefault4K():54:57 -> createDefault4K
    1:1:int getBitRate():160:160 -> getBitRate
    1:1:com.android.rockchip.camera2.video.TpVideoConfig$BitrateMode getBitrateMode():163:163 -> getBitrateMode
    1:1:com.android.rockchip.camera2.video.TpVideoConfig$VideoCodec getCodec():161:161 -> getCodec
    1:1:int getFrameRate():159:159 -> getFrameRate
    1:1:int getHeight():158:158 -> getHeight
    1:1:int getKeyFrameInterval():162:162 -> getKeyFrameInterval
    1:1:android.util.Size getSize():166:166 -> getSize
    1:1:int getWidth():157:157 -> getWidth
    1:2:java.lang.String toString():173:174 -> toString
    3:3:java.lang.String toString():173:173 -> toString
com.android.rockchip.camera2.video.TpVideoConfig$BitrateMode -> com.android.rockchip.camera2.video.TpVideoConfig$BitrateMode:
# {"id":"sourceFile","fileName":"TpVideoConfig.java"}
    1:1:com.android.rockchip.camera2.video.TpVideoConfig$BitrateMode[] $values():202:202 -> $values
    1:3:void <clinit>():203:205 -> <clinit>
    4:4:void <clinit>():202:202 -> <clinit>
    1:2:void <init>(java.lang.String,int,int):209:210 -> <init>
    1:1:int getValue():214:214 -> getValue
    1:1:com.android.rockchip.camera2.video.TpVideoConfig$BitrateMode valueOf(java.lang.String):202:202 -> valueOf
    1:1:com.android.rockchip.camera2.video.TpVideoConfig$BitrateMode[] values():202:202 -> values
com.android.rockchip.camera2.video.TpVideoConfig$Builder -> com.android.rockchip.camera2.video.TpVideoConfig$Builder:
# {"id":"sourceFile","fileName":"TpVideoConfig.java"}
    1:1:void <init>(int,int):79:79 -> <init>
    2:15:void <init>(int,int):70:83 -> <init>
    1:3:com.android.rockchip.camera2.video.TpVideoConfig build():113:115 -> build
    1:1:int calculateDefaultBitRate(int,int,int):150:150 -> calculateDefaultBitRate
    1:1:com.android.rockchip.camera2.video.TpVideoConfig$Builder setBitRate(int):87:87 -> setBitRate
    1:1:com.android.rockchip.camera2.video.TpVideoConfig$Builder setBitrateMode(com.android.rockchip.camera2.video.TpVideoConfig$BitrateMode):102:102 -> setBitrateMode
    1:1:com.android.rockchip.camera2.video.TpVideoConfig$Builder setCodec(com.android.rockchip.camera2.video.TpVideoConfig$VideoCodec):92:92 -> setCodec
    1:1:com.android.rockchip.camera2.video.TpVideoConfig$Builder setKeyFrameInterval(int):97:97 -> setKeyFrameInterval
    1:8:void validateParameters():122:129 -> validateParameters
    9:9:void validateParameters():126:126 -> validateParameters
    10:10:void validateParameters():123:123 -> validateParameters
com.android.rockchip.camera2.video.TpVideoConfig$VideoCodec -> com.android.rockchip.camera2.video.TpVideoConfig$VideoCodec:
# {"id":"sourceFile","fileName":"TpVideoConfig.java"}
    1:1:com.android.rockchip.camera2.video.TpVideoConfig$VideoCodec[] $values():184:184 -> $values
    1:2:void <clinit>():185:186 -> <clinit>
    3:3:void <clinit>():184:184 -> <clinit>
    1:2:void <init>(java.lang.String,int,java.lang.String):190:191 -> <init>
    1:1:java.lang.String getMimeType():195:195 -> getMimeType
    1:1:com.android.rockchip.camera2.video.TpVideoConfig$VideoCodec valueOf(java.lang.String):184:184 -> valueOf
    1:1:com.android.rockchip.camera2.video.TpVideoConfig$VideoCodec[] values():184:184 -> values
com.android.rockchip.camera2.video.TpVideoSystem -> com.android.rockchip.camera2.video.TpVideoSystem:
# {"id":"sourceFile","fileName":"TpVideoSystem.java"}
    1:1:void <init>(androidx.appcompat.app.AppCompatActivity):223:223 -> <init>
    2:2:void <init>(androidx.appcompat.app.AppCompatActivity,com.android.rockchip.camera2.video.TpVideoConfig):231:231 -> <init>
    3:183:void <init>(androidx.appcompat.app.AppCompatActivity,com.android.rockchip.camera2.video.TpVideoConfig):59:239 -> <init>
    1:16:void captureImage(java.lang.String):341:356 -> captureImage
    1:11:boolean checkCameraOperationPreconditions(java.lang.String):1278:1288 -> checkCameraOperationPreconditions
    12:12:boolean checkCameraOperationPreconditions(java.lang.String):1281:1281 -> checkCameraOperationPreconditions
    1:1:com.android.rockchip.camera2.service.StreamingService$HeartbeatListener createHeartbeatListener():1449:1449 -> createHeartbeatListener
    1:4:boolean executeStreamingOperation(com.android.rockchip.camera2.video.TpVideoSystem$StreamingOperation):1564:1567 -> executeStreamingOperation
    1:2:java.lang.Object getAdvancedComponent(java.lang.Object,java.lang.String):1299:1300 -> getAdvancedComponent
    1:1:com.android.rockchip.camera2.video.TpCameraManager getCameraManager():1121:1121 -> getCameraManager
    1:6:com.android.rockchip.camera2.video.TpVideoSystem$StreamType getCurrentStreamType():680:685 -> getCurrentStreamType
    7:8:com.android.rockchip.camera2.video.TpVideoSystem$StreamType com.android.rockchip.camera2.video.TpVideoSystem$StreamType.fromInternalType(com.android.rockchip.camera2.service.StreamingService$StreamType):114:115 -> getCurrentStreamType
    7:8:com.android.rockchip.camera2.video.TpVideoSystem$StreamType getCurrentStreamType():685 -> getCurrentStreamType
    1:1:java.lang.String getCurrentVideoPath():985:985 -> getCurrentVideoPath
    1:2:float getCurrentVideoPlaybackSpeed():1017:1018 -> getCurrentVideoPlaybackSpeed
    1:2:long getCurrentVideoPosition():961:962 -> getCurrentVideoPosition
    1:1:com.android.rockchip.camera2.video.TpCaptureImage getImageCapture():1134:1134 -> getImageCapture
    1:1:java.lang.String getStreamUrl():672:672 -> getStreamUrl
    1:1:com.android.rockchip.camera2.service.StreamingService getStreamingService():1185:1185 -> getStreamingService
    1:2:com.android.rockchip.camera2.video.TvPreviewHelper getTvPreviewHelper():1198:1199 -> getTvPreviewHelper
    1:1:com.android.rockchip.camera2.video.TpVideoConfig getVideoConfig():435:435 -> getVideoConfig
    1:4:com.android.rockchip.camera2.video.VideoDecoder getVideoDecoder():1169:1172 -> getVideoDecoder
    1:2:long getVideoDuration():973:974 -> getVideoDuration
    1:1:com.android.rockchip.camera2.video.VideoEncoder getVideoEncoder():1108:1108 -> getVideoEncoder
    1:10:void initCaptureHelper():1311:1320 -> initCaptureHelper
    1:15:void initStreamingServiceEarly():1395:1409 -> initStreamingServiceEarly
    1:2:void initTvPreviewHelperIfNeeded():1469:1470 -> initTvPreviewHelperIfNeeded
    1:61:void initVideoEncoder(android.view.Surface):1327:1387 -> initVideoEncoder
    1:20:void initialize(android.view.Surface):257:276 -> initialize
    1:22:void initializeStreamingService():1419:1440 -> initializeStreamingService
    1:1:boolean isCameraStarted():371:371 -> isCameraStarted
    1:2:boolean isCurrentVideoPlaybackCompleted():1071:1072 -> isCurrentVideoPlaybackCompleted
    1:1:boolean isInitialized():378:378 -> isInitialized
    1:1:boolean isRecording():364:364 -> isRecording
    1:1:boolean isStreaming():664:664 -> isStreaming
    1:1:boolean isTvMode():800:800 -> isTvMode
    1:1:boolean isVideoPlaying():952:952 -> isVideoPlaying
    1:1:void lambda$captureImage$4(java.lang.Exception,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener):356:356 -> lambda$captureImage$4
    1:1:void lambda$checkCameraOperationPreconditions$20(java.lang.String,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener):1281:1281 -> lambda$checkCameraOperationPreconditions$20
    1:1:void lambda$checkCameraOperationPreconditions$21(java.lang.String,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener):1288:1288 -> lambda$checkCameraOperationPreconditions$21
    1:1:void lambda$executeStreamingOperation$40(java.lang.Exception,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener):1567:1567 -> lambda$executeStreamingOperation$40
    1:1:void lambda$initCaptureHelper$22(java.lang.String,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener):1314:1314 -> lambda$initCaptureHelper$22
    1:2:void lambda$initCaptureHelper$23(java.lang.String):1313:1314 -> lambda$initCaptureHelper$23
    1:1:void lambda$initCaptureHelper$24(java.lang.String,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener):1318:1318 -> lambda$initCaptureHelper$24
    1:2:void lambda$initCaptureHelper$25(java.lang.String):1317:1318 -> lambda$initCaptureHelper$25
    1:4:void lambda$initVideoEncoder$26(android.view.Surface,android.hardware.camera2.CameraDevice):1337:1340 -> lambda$initVideoEncoder$26
    5:17:void lambda$initVideoEncoder$26(android.view.Surface,android.hardware.camera2.CameraDevice):1337:1349 -> lambda$initVideoEncoder$26
    1:2:void lambda$initVideoEncoder$27(android.hardware.camera2.CameraDevice):1352:1353 -> lambda$initVideoEncoder$27
    1:1:void lambda$initVideoEncoder$28(java.lang.Integer,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener):1360:1360 -> lambda$initVideoEncoder$28
    1:4:void lambda$initVideoEncoder$29(android.hardware.camera2.CameraDevice,java.lang.Integer):1357:1360 -> lambda$initVideoEncoder$29
    1:32:void lambda$initVideoEncoder$30(android.view.Surface):1334:1365 -> lambda$initVideoEncoder$30
    1:1:void lambda$initVideoEncoder$31(com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener):1370:1370 -> lambda$initVideoEncoder$31
    1:2:void lambda$initVideoEncoder$32():1369:1370 -> lambda$initVideoEncoder$32
    1:1:void lambda$initVideoEncoder$33(java.lang.String,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener):1375:1375 -> lambda$initVideoEncoder$33
    1:3:void lambda$initVideoEncoder$34(java.lang.String,java.lang.Exception):1373:1375 -> lambda$initVideoEncoder$34
    1:1:void lambda$initVideoEncoder$35(com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener):1380:1380 -> lambda$initVideoEncoder$35
    1:2:void lambda$initVideoEncoder$36():1379:1380 -> lambda$initVideoEncoder$36
    1:1:void lambda$initVideoEncoder$37(java.lang.String,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener):1385:1385 -> lambda$initVideoEncoder$37
    1:3:void lambda$initVideoEncoder$38(java.lang.String):1383:1385 -> lambda$initVideoEncoder$38
    1:1:void lambda$initialize$0(java.lang.Exception,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener):276:276 -> lambda$initialize$0
    1:1:void lambda$initializeStreamingService$39(java.lang.Exception,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener):1440:1440 -> lambda$initializeStreamingService$39
    1:1:void lambda$loadFullImage$7(com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener):416:416 -> lambda$loadFullImage$7
    1:1:void lambda$loadFullImage$8(java.lang.Exception,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener):425:425 -> lambda$loadFullImage$8
    1:1:void lambda$loadThumbnail$5(com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener):393:393 -> lambda$loadThumbnail$5
    1:1:void lambda$loadThumbnail$6(java.lang.Exception,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener):402:402 -> lambda$loadThumbnail$6
    1:1:void lambda$playVideo$14(com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener):847:847 -> lambda$playVideo$14
    1:1:void lambda$playVideo$15(com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener):853:853 -> lambda$playVideo$15
    1:1:void lambda$playVideo$16(java.lang.String,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener):883:883 -> lambda$playVideo$16
    1:1:void lambda$playVideo$17(java.lang.Exception,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener):888:888 -> lambda$playVideo$17
    1:1:void lambda$releaseVideo$18(com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener):922:922 -> lambda$releaseVideo$18
    1:1:void lambda$releaseVideo$19(java.lang.Exception,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener):925:925 -> lambda$releaseVideo$19
    1:1:void lambda$startRecording$1(java.lang.String,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener):306:306 -> lambda$startRecording$1
    1:1:void lambda$startRecording$2(java.lang.Exception,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener):309:309 -> lambda$startRecording$2
    1:1:boolean lambda$startStreaming$10(com.android.rockchip.camera2.video.TpVideoSystem$StreamType,java.lang.String):638:638 -> lambda$startStreaming$10
    2:4:com.android.rockchip.camera2.service.StreamingService$StreamType com.android.rockchip.camera2.video.TpVideoSystem$StreamType.toInternalType():103:105 -> lambda$startStreaming$10
    2:4:boolean lambda$startStreaming$10(com.android.rockchip.camera2.video.TpVideoSystem$StreamType,java.lang.String):638 -> lambda$startStreaming$10
    5:5:boolean lambda$startStreaming$10(com.android.rockchip.camera2.video.TpVideoSystem$StreamType,java.lang.String):639:639 -> lambda$startStreaming$10
    1:1:void lambda$startStreaming$9(com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener):629:629 -> lambda$startStreaming$9
    1:1:void lambda$stopRecording$3(java.lang.Exception,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener):330:330 -> lambda$stopRecording$3
    1:1:boolean lambda$stopStreaming$11():656:656 -> lambda$stopStreaming$11
    1:1:void lambda$switchToCameraMode$13(java.lang.Exception,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener):789:789 -> lambda$switchToCameraMode$13
    1:1:void lambda$switchToTvMode$12(java.lang.Exception,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener):751:751 -> lambda$switchToTvMode$12
    1:5:void loadFullImage(java.lang.String,android.widget.ImageView):421:425 -> loadFullImage
    6:6:void loadFullImage(java.lang.String,android.widget.ImageView):416:416 -> loadFullImage
    1:5:void loadThumbnail(java.lang.String,android.widget.ImageView):398:402 -> loadThumbnail
    6:6:void loadThumbnail(java.lang.String,android.widget.ImageView):393:393 -> loadThumbnail
    1:2:void notifyListener(com.android.rockchip.camera2.video.TpVideoSystem$ListenerAction):1261:1262 -> notifyListener
    1:2:void pauseVideo():897:898 -> pauseVideo
    1:44:boolean playVideo(java.lang.String,android.view.Surface):845:888 -> playVideo
    45:45:boolean playVideo(java.lang.String,android.view.Surface):847:847 -> playVideo
    1:40:void release():1211:1250 -> release
    1:12:void releaseVideo():917:928 -> releaseVideo
    13:18:void releaseVideo():925:930 -> releaseVideo
    1:2:boolean resetCurrentVideoToStart():1086:1087 -> resetCurrentVideoToStart
    1:2:void resumeVideo():907:908 -> resumeVideo
    1:3:boolean seekCurrentVideoRelative(long):1053:1055 -> seekCurrentVideoRelative
    1:3:void seekVideoTo(long):940:942 -> seekVideoTo
    1:3:boolean setCurrentVideoPlaybackSpeed(float):1001:1003 -> setCurrentVideoPlaybackSpeed
    1:1:void setListener(com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener):247:247 -> setListener
    1:1:void setStreamType(com.android.rockchip.camera2.video.TpVideoSystem$StreamType):700:700 -> setStreamType
    2:4:com.android.rockchip.camera2.service.StreamingService$StreamType com.android.rockchip.camera2.video.TpVideoSystem$StreamType.toInternalType():103:105 -> setStreamType
    2:4:void setStreamType(com.android.rockchip.camera2.video.TpVideoSystem$StreamType):702 -> setStreamType
    5:9:void setStreamType(com.android.rockchip.camera2.video.TpVideoSystem$StreamType):703:707 -> setStreamType
    1:7:void setTvContainer(android.view.ViewGroup):815:821 -> setTvContainer
    1:19:void startCameraPreview():1531:1549 -> startCameraPreview
    20:20:void startCameraPreview():1532:1532 -> startCameraPreview
    1:24:void startRecording(java.lang.String):286:309 -> startRecording
    1:1:boolean startStreaming():608:608 -> startStreaming
    2:2:boolean startStreaming(com.android.rockchip.camera2.video.TpVideoSystem$StreamType):617:617 -> startStreaming
    3:12:boolean startStreaming(com.android.rockchip.camera2.video.TpVideoSystem$StreamType,java.lang.String):627:636 -> startStreaming
    1:8:void startTvPreview():1482:1489 -> startTvPreview
    9:9:void startTvPreview():1485:1485 -> startTvPreview
    1:2:boolean stepCurrentVideoFrame():1033:1034 -> stepCurrentVideoFrame
    1:8:void stopCameraPreview():1512:1519 -> stopCameraPreview
    1:14:void stopRecording():317:330 -> stopRecording
    1:8:boolean stopStreaming():648:655 -> stopStreaming
    1:2:void stopTvPreview():1499:1500 -> stopTvPreview
    1:22:void switchToCameraMode():769:790 -> switchToCameraMode
    1:26:void switchToTvMode():727:752 -> switchToTvMode
    27:27:void switchToTvMode():733:733 -> switchToTvMode
    1:30:boolean updateBitRate(int):469:498 -> updateBitRate
    31:31:boolean updateBitRate(int):465:465 -> updateBitRate
    1:12:void updateConfigBitRate(int):509:520 -> updateConfigBitRate
    1:40:boolean updateResolution(int,int):547:586 -> updateResolution
    1:6:void updateVideoConfig(com.android.rockchip.camera2.video.TpVideoConfig):442:447 -> updateVideoConfig
    7:7:void updateVideoConfig(com.android.rockchip.camera2.video.TpVideoConfig):443:443 -> updateVideoConfig
com.android.rockchip.camera2.video.TpVideoSystem$1 -> com.android.rockchip.camera2.video.TpVideoSystem$1:
# {"id":"sourceFile","fileName":"TpVideoSystem.java"}
    1:1:void <init>(com.android.rockchip.camera2.video.TpVideoSystem,java.lang.String):867:867 -> <init>
    1:1:void lambda$onPlaybackCompleted$0(java.lang.String,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener):871:871 -> lambda$onPlaybackCompleted$0
    1:2:void onPlaybackCompleted():870:871 -> onPlaybackCompleted
com.android.rockchip.camera2.video.TpVideoSystem$2 -> com.android.rockchip.camera2.video.TpVideoSystem$2:
# {"id":"sourceFile","fileName":"TpVideoSystem.java"}
    1:1:void <init>(com.android.rockchip.camera2.video.TpVideoSystem):1449:1449 -> <init>
    1:1:void lambda$onStreamError$1(java.lang.String,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener):1458:1458 -> lambda$onStreamError$1
    1:1:void lambda$onStreamStatusChanged$0(boolean,java.lang.String,com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener):1453:1453 -> lambda$onStreamStatusChanged$0
    1:1:void onStreamError(java.lang.String):1458:1458 -> onStreamError
    1:1:void onStreamStatusChanged(boolean,java.lang.String):1453:1453 -> onStreamStatusChanged
com.android.rockchip.camera2.video.TpVideoSystem$ListenerAction -> com.android.rockchip.camera2.video.TpVideoSystem$ListenerAction:
# {"id":"sourceFile","fileName":"TpVideoSystem.java"}
com.android.rockchip.camera2.video.TpVideoSystem$StreamType -> com.android.rockchip.camera2.video.TpVideoSystem$StreamType:
# {"id":"sourceFile","fileName":"TpVideoSystem.java"}
    1:1:com.android.rockchip.camera2.video.TpVideoSystem$StreamType[] $values():92:92 -> $values
    1:3:void <clinit>():94:96 -> <clinit>
    4:4:void <clinit>():92:92 -> <clinit>
    1:1:void <init>(java.lang.String,int):92:92 -> <init>
    1:1:com.android.rockchip.camera2.video.TpVideoSystem$StreamType valueOf(java.lang.String):92:92 -> valueOf
    1:1:com.android.rockchip.camera2.video.TpVideoSystem$StreamType[] values():92:92 -> values
com.android.rockchip.camera2.video.TpVideoSystem$StreamingOperation -> com.android.rockchip.camera2.video.TpVideoSystem$StreamingOperation:
# {"id":"sourceFile","fileName":"TpVideoSystem.java"}
com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemAdapter -> com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemAdapter:
# {"id":"sourceFile","fileName":"TpVideoSystem.java"}
    1:1:void <init>():207:207 -> <init>
    1:1:void onError(java.lang.String):211:211 -> onError
com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener -> com.android.rockchip.camera2.video.TpVideoSystem$TpVideoSystemListener:
# {"id":"sourceFile","fileName":"TpVideoSystem.java"}
com.android.rockchip.camera2.video.TvPreviewHelper -> com.android.rockchip.camera2.video.TvPreviewHelper:
# {"id":"sourceFile","fileName":"TvPreviewHelper.java"}
    1:1:void <init>(android.content.Context,android.view.ViewGroup):54:54 -> <init>
    2:22:void <init>(android.content.Context,android.view.ViewGroup):37:57 -> <init>
    1:13:void initTvView():65:77 -> initTvView
    1:1:boolean isActive():117:117 -> isActive
    1:2:void release():107:108 -> release
    1:3:void startPreview():84:86 -> startPreview
    1:5:void stopPreview():94:98 -> stopPreview
com.android.rockchip.camera2.video.TvPreviewHelper$1 -> com.android.rockchip.camera2.video.e:
# {"id":"sourceFile","fileName":"TvPreviewHelper.java"}
    com.android.rockchip.camera2.video.TvPreviewHelper this$0 -> a
    1:1:void <init>(com.android.rockchip.camera2.video.TvPreviewHelper,android.os.Looper):37:37 -> <init>
    1:4:void handleMessage(android.os.Message):39:42 -> handleMessage
com.android.rockchip.camera2.video.VideoDecoder -> com.android.rockchip.camera2.video.VideoDecoder:
# {"id":"sourceFile","fileName":"VideoDecoder.java"}
    1:1:void <init>(java.lang.String,android.view.Surface):109:109 -> <init>
    2:52:void <init>(java.lang.String,android.view.Surface):61:111 -> <init>
    1:24:void decodeSingleFrame():455:478 -> decodeSingleFrame
    1:106:void decodingTask():220:325 -> decodingTask
    107:107:void decodingTask():228:228 -> decodingTask
    1:1:long getCurrentPosition():612:612 -> getCurrentPosition
    1:1:float getPlaybackSpeed():662:662 -> getPlaybackSpeed
    1:1:long getVideoDuration():599:599 -> getVideoDuration
    1:1:boolean isDecoding():560:560 -> isDecoding
    1:1:boolean isFrameByFrame():586:586 -> isFrameByFrame
    1:1:boolean isPaused():573:573 -> isPaused
    1:1:boolean isPlaybackCompleted():678:678 -> isPlaybackCompleted
    1:1:void lambda$stepFrame$0():440:440 -> lambda$stepFrame$0
    1:2:void resetToStart():669:670 -> resetToStart
    1:1:void safeSleep(long):628:628 -> safeSleep
    1:3:void seekRelative(long):498:500 -> seekRelative
    1:39:void seekTo(long):352:390 -> seekTo
    1:10:int selectVideoTrack(android.media.MediaExtractor):189:198 -> selectVideoTrack
    1:1:void setPlaybackListener(com.android.rockchip.camera2.video.VideoDecoder$VideoDecoderListener):87:87 -> setPlaybackListener
    1:5:void setPlaybackSpeed(float):647:651 -> setPlaybackSpeed
    1:35:void startDecoding():129:163 -> startDecoding
    1:11:void stepFrame():431:441 -> stepFrame
    1:25:void stopDecoding():519:543 -> stopDecoding
    1:16:void togglePlayPause():403:418 -> togglePlayPause
com.android.rockchip.camera2.video.VideoDecoder$VideoDecoderListener -> com.android.rockchip.camera2.video.VideoDecoder$VideoDecoderListener:
# {"id":"sourceFile","fileName":"VideoDecoder.java"}
com.android.rockchip.camera2.video.VideoEncoder -> com.android.rockchip.camera2.video.VideoEncoder:
# {"id":"sourceFile","fileName":"VideoEncoder.java"}
    1:1:void <init>():212:212 -> <init>
    2:32:void <init>():81:111 -> <init>
    1:1:com.android.rockchip.camera2.video.VideoEncoder$Builder builder():224:224 -> builder
    1:37:android.media.MediaFormat createMediaFormatFromTpVideoConfig(com.android.rockchip.camera2.video.TpVideoConfig,android.util.Size):946:982 -> createMediaFormatFromTpVideoConfig
    1:2:long getCurrentFileSize():719:720 -> getCurrentFileSize
    1:7:android.media.MediaFormat getEncoderOutputFormat():576:582 -> getEncoderOutputFormat
    1:50:void initialize(android.util.Size,android.view.Surface):512:561 -> initialize
    1:1:void lambda$notifySaveComplete$0(java.lang.String):289:289 -> lambda$notifySaveComplete$0
    1:76:void lambda$startEncodingDecoding$3():850:925 -> lambda$startEncodingDecoding$3
    1:1:void lambda$stopRecording$1(java.lang.Exception):780:780 -> lambda$stopRecording$1
    1:29:void lambda$stopRecording$2(java.lang.String):760:788 -> lambda$stopRecording$2
    30:30:void lambda$stopRecording$2(java.lang.String):788:788 -> lambda$stopRecording$2
    31:42:void lambda$stopRecording$2(java.lang.String):777:788 -> lambda$stopRecording$2
    43:43:void lambda$stopRecording$2(java.lang.String):788:788 -> lambda$stopRecording$2
    44:47:void lambda$stopRecording$2(java.lang.String):786:789 -> lambda$stopRecording$2
    48:48:void lambda$stopRecording$2(java.lang.String):788:788 -> lambda$stopRecording$2
    1:9:int mapBitrateModeToEncoder(com.android.rockchip.camera2.video.TpVideoConfig$BitrateMode):1005:1013 -> mapBitrateModeToEncoder
    1:8:java.lang.String mapVideoCodecToMimeType(com.android.rockchip.camera2.video.TpVideoConfig$VideoCodec):990:997 -> mapVideoCodecToMimeType
    9:9:java.lang.String mapVideoCodecToMimeType(com.android.rockchip.camera2.video.TpVideoConfig$VideoCodec):994:994 -> mapVideoCodecToMimeType
    10:10:java.lang.String mapVideoCodecToMimeType(com.android.rockchip.camera2.video.TpVideoConfig$VideoCodec):992:992 -> mapVideoCodecToMimeType
    1:3:void notifyError(java.lang.String,java.lang.Exception):259:261 -> notifyError
    1:3:void notifyFileSizeLimitReached():270:272 -> notifyFileSizeLimitReached
    1:5:void notifySaveComplete(java.lang.String):283:287 -> notifySaveComplete
    1:3:void notifyStorageFull():245:247 -> notifyStorageFull
    1:3:void notifySurfaceAvailable(android.view.Surface):234:236 -> notifySurfaceAvailable
    1:33:void release():805:837 -> release
    1:5:void requestKeyFrame():604:608 -> requestKeyFrame
    1:17:boolean setBitrate(int):151:167 -> setBitrate
    1:1:void setOutputCallback(com.android.rockchip.camera2.video.VideoEncoder$VideoDataOutputCallback):204:204 -> setOutputCallback
    1:86:void startEncodingDecoding():848:933 -> startEncodingDecoding
    1:28:void startRecording(java.lang.String):623:650 -> startRecording
    29:29:void startRecording(java.lang.String):645:645 -> startRecording
    1:44:void startStorageMonitoring():662:705 -> startStorageMonitoring
    1:64:void stopRecording():731:794 -> stopRecording
    65:65:void stopRecording():794:794 -> stopRecording
com.android.rockchip.camera2.video.VideoEncoder$1 -> com.android.rockchip.camera2.video.f:
# {"id":"sourceFile","fileName":"VideoEncoder.java"}
    com.android.rockchip.camera2.video.VideoEncoder this$0 -> a
    1:1:void <init>(com.android.rockchip.camera2.video.VideoEncoder):664:664 -> <init>
    1:2:void handleFileSizeLimit():701:702 -> handleFileSizeLimit
    1:3:void handleStorageError(java.lang.String):690:692 -> handleStorageError
    1:12:void run():668:679 -> run
com.android.rockchip.camera2.video.VideoEncoder$2 -> com.android.rockchip.camera2.video.g:
# {"id":"sourceFile","fileName":"VideoEncoder.java"}
    int[] $SwitchMap$com$android$rockchip$camera2$video$TpVideoConfig$VideoCodec -> a
    int[] $SwitchMap$com$android$rockchip$camera2$video$TpVideoConfig$BitrateMode -> b
    1:1:void <clinit>():1005:1005 -> <clinit>
    2:2:void <clinit>():990:990 -> <clinit>
com.android.rockchip.camera2.video.VideoEncoder$Builder -> com.android.rockchip.camera2.video.VideoEncoder$Builder:
# {"id":"sourceFile","fileName":"VideoEncoder.java"}
    1:2:void <init>():331:332 -> <init>
    1:18:com.android.rockchip.camera2.video.VideoEncoder build():478:495 -> build
    19:19:com.android.rockchip.camera2.video.VideoEncoder build():484:484 -> build
    1:1:com.android.rockchip.camera2.video.VideoEncoder$Builder onError(java.util.function.BiConsumer):413:413 -> onError
    1:1:com.android.rockchip.camera2.video.VideoEncoder$Builder onFileSizeLimitReached(java.lang.Runnable):429:429 -> onFileSizeLimitReached
    1:1:com.android.rockchip.camera2.video.VideoEncoder$Builder onSaveComplete(java.util.function.Consumer):445:445 -> onSaveComplete
    1:1:com.android.rockchip.camera2.video.VideoEncoder$Builder onStorageFull(java.lang.Runnable):397:397 -> onStorageFull
    1:1:com.android.rockchip.camera2.video.VideoEncoder$Builder onSurfaceAvailable(java.util.function.Consumer):381:381 -> onSurfaceAvailable
    1:1:com.android.rockchip.camera2.video.VideoEncoder$Builder setPreviewSurface(android.view.Surface):365:365 -> setPreviewSurface
    1:1:com.android.rockchip.camera2.video.VideoEncoder$Builder setSize(android.util.Size):350:350 -> setSize
    1:2:com.android.rockchip.camera2.video.VideoEncoder$Builder setTpVideoConfig(com.android.rockchip.camera2.video.TpVideoConfig):461:462 -> setTpVideoConfig
com.android.rockchip.camera2.video.VideoEncoder$VideoDataOutputCallback -> com.android.rockchip.camera2.video.VideoEncoder$VideoDataOutputCallback:
# {"id":"sourceFile","fileName":"VideoEncoder.java"}
com.android.rockchip.camera2.view.TpCustomProgressBar -> com.android.rockchip.camera2.view.TpCustomProgressBar:
# {"id":"sourceFile","fileName":"TpCustomProgressBar.java"}
    1:1:void <init>(android.content.Context):60:60 -> <init>
    2:32:void <init>(android.content.Context):31:61 -> <init>
    33:33:void <init>(android.content.Context,android.util.AttributeSet):65:65 -> <init>
    34:69:void <init>(android.content.Context,android.util.AttributeSet):31:66 -> <init>
    70:70:void <init>(android.content.Context,android.util.AttributeSet,int) -> <init>
    71:111:void <init>(android.content.Context,android.util.AttributeSet,int):31:71 -> <init>
    1:32:void animateThumbPress(boolean):216:247 -> animateThumbPress
    1:1:int getMax():282:282 -> getMax
    1:1:int getProgress():268:268 -> getProgress
    1:1:int getSecondaryProgress():294:294 -> getSecondaryProgress
    1:21:void init():76:96 -> init
    1:12:void lambda$animateThumbPress$0(float,float,float,float,float,float,android.animation.ValueAnimator):233:244 -> lambda$animateThumbPress$0
    1:48:void onDraw(android.graphics.Canvas):109:156 -> onDraw
    1:4:void onMeasure(int,int):101:104 -> onMeasure
    1:31:boolean onTouchEvent(android.view.MotionEvent):161:191 -> onTouchEvent
    32:40:boolean onTouchEvent(android.view.MotionEvent):167:175 -> onTouchEvent
    1:5:void setMax(int):273:277 -> setMax
    1:1:void setOnProgressChangeListener(com.android.rockchip.camera2.view.TpCustomProgressBar$OnProgressChangeListener):298:298 -> setOnProgressChangeListener
    1:1:void setProgress(int):252:252 -> setProgress
    2:8:void setProgress(int,boolean):256:262 -> setProgress
    1:4:void setSecondaryProgress(int):286:289 -> setSecondaryProgress
    1:11:void updateProgressFromTouch(android.view.MotionEvent):200:210 -> updateProgressFromTouch
com.android.rockchip.camera2.view.TpCustomProgressBar$OnProgressChangeListener -> com.android.rockchip.camera2.view.TpCustomProgressBar$OnProgressChangeListener:
# {"id":"sourceFile","fileName":"TpCustomProgressBar.java"}
com.android.rockchip.camera2.view.TpImageView -> com.android.rockchip.camera2.view.TpImageView:
# {"id":"sourceFile","fileName":"TpImageView.java"}
    com.android.rockchip.camera2.view.TpImageView$GestureState mGestureState -> mGestureState
      # {"id":"com.android.tools.r8.residualsignature","signature":"Lcom/android/rockchip/camera2/view/c;"}
    com.android.rockchip.camera2.view.TpImageView$ScaleInfo mScaleInfo -> mScaleInfo
      # {"id":"com.android.tools.r8.residualsignature","signature":"Lcom/android/rockchip/camera2/view/d;"}
    1:1:void <init>(android.content.Context):139:139 -> <init>
    2:33:void <init>(android.content.Context):109:140 -> <init>
    34:34:void <init>(android.content.Context,android.util.AttributeSet):144:144 -> <init>
    35:71:void <init>(android.content.Context,android.util.AttributeSet):109:145 -> <init>
    72:72:void <init>(android.content.Context,android.util.AttributeSet,int):149:149 -> <init>
    73:114:void <init>(android.content.Context,android.util.AttributeSet,int):109:150 -> <init>
    1:3:void cancelResetGestureState():360:362 -> cancelResetGestureState
    1:32:android.view.ScaleGestureDetector createOptimizedScaleGestureDetector(android.content.Context):201:232 -> createOptimizedScaleGestureDetector
    1:3:float getCurrentScale():340:342 -> getCurrentScale
    1:1:float getFitScreenScale():507:507 -> getFitScreenScale
    1:1:float getMaxScale():494:494 -> getMaxScale
    1:1:float getMinScale():516:516 -> getMinScale
    1:24:boolean handlePanGesture(android.view.MotionEvent):268:291 -> handlePanGesture
    25:27:boolean handlePanGesture(android.view.MotionEvent):270:272 -> handlePanGesture
    1:4:void init(android.content.Context):158:161 -> init
    1:4:void initGestureDetectors(android.content.Context):171:174 -> initGestureDetectors
    1:1:boolean isDoubleTapEnabled():485:485 -> isDoubleTapEnabled
    1:1:boolean isPanEnabled():478:478 -> isPanEnabled
    1:1:boolean isZoomEnabled():471:471 -> isZoomEnabled
    1:2:void lambda$scheduleResetGestureState$0():351:352 -> lambda$scheduleResetGestureState$0
    1:2:void onSizeChanged(int,int,int,int):368:369 -> onSizeChanged
    1:15:boolean onTouchEvent(android.view.MotionEvent):247:261 -> onTouchEvent
    1:31:boolean performScale(float,float,float):302:332 -> performScale
    1:18:void resetMatrix():382:399 -> resetMatrix
    19:30:void com.android.rockchip.camera2.view.TpImageView$ScaleInfo.updateFitScreenScale(float,float,float,float):79:90 -> resetMatrix
    19:30:void resetMatrix():399 -> resetMatrix
    31:44:void resetMatrix():402:415 -> resetMatrix
    45:46:void resetMatrix():387:388 -> resetMatrix
    1:8:void scheduleResetGestureState():349:356 -> scheduleResetGestureState
    1:2:void setDoubleTapEnabled(boolean):456:457 -> setDoubleTapEnabled
    1:2:void setImageDrawable(android.graphics.drawable.Drawable):374:375 -> setImageDrawable
    1:1:void setMaxScale(float):431:431 -> setMaxScale
    2:2:void com.android.rockchip.camera2.view.TpImageView$ScaleInfo.setUserMaxScale(float):95:95 -> setMaxScale
    2:2:void setMaxScale(float):431 -> setMaxScale
      # {"id":"com.android.tools.r8.rewriteFrame","conditions":["throws(Ljava/lang/NullPointerException;)"],"actions":["removeInnerFrames(1)"]}
    3:3:void com.android.rockchip.camera2.view.TpImageView$ScaleInfo.setUserMaxScale(float):96:96 -> setMaxScale
    3:3:void setMaxScale(float):431 -> setMaxScale
    4:4:void setMaxScale(float):432:432 -> setMaxScale
    1:1:void setOnZoomChangeListener(com.android.rockchip.camera2.view.TpImageView$OnZoomChangeListener):464:464 -> setOnZoomChangeListener
    1:2:void setPanEnabled(boolean):448:449 -> setPanEnabled
    1:2:void setZoomEnabled(boolean):440:441 -> setZoomEnabled
com.android.rockchip.camera2.view.TpImageView$1 -> com.android.rockchip.camera2.view.a:
# {"id":"sourceFile","fileName":"TpImageView.java"}
    com.android.rockchip.camera2.view.TpImageView this$0 -> a
    1:1:void <init>(com.android.rockchip.camera2.view.TpImageView):174:174 -> <init>
    1:3:boolean onDoubleTap(android.view.MotionEvent):177:179 -> onDoubleTap
    4:4:float com.android.rockchip.camera2.view.TpImageView$ScaleInfo.getNextDoubleTapScale():102:102 -> onDoubleTap
    4:4:boolean onDoubleTap(android.view.MotionEvent):179 -> onDoubleTap
      # {"id":"com.android.tools.r8.rewriteFrame","conditions":["throws(Ljava/lang/NullPointerException;)"],"actions":["removeInnerFrames(1)"]}
    5:9:boolean onDoubleTap(android.view.MotionEvent):180:184 -> onDoubleTap
    1:1:boolean onSingleTapConfirmed(android.view.MotionEvent):189:189 -> onSingleTapConfirmed
com.android.rockchip.camera2.view.TpImageView$2 -> com.android.rockchip.camera2.view.b:
# {"id":"sourceFile","fileName":"TpImageView.java"}
    com.android.rockchip.camera2.view.TpImageView this$0 -> a
    1:1:void <init>(com.android.rockchip.camera2.view.TpImageView):201:201 -> <init>
    1:7:boolean onScale(android.view.ScaleGestureDetector):204:210 -> onScale
    1:2:boolean onScaleBegin(android.view.ScaleGestureDetector):215:216 -> onScaleBegin
    1:1:void onScaleEnd(android.view.ScaleGestureDetector):223:223 -> onScaleEnd
com.android.rockchip.camera2.view.TpImageView$GestureState -> com.android.rockchip.camera2.view.c:
# {"id":"sourceFile","fileName":"TpImageView.java"}
    com.android.rockchip.camera2.view.TpImageView$GestureState[] $VALUES -> $VALUES
      # {"id":"com.android.tools.r8.residualsignature","signature":"[Lcom/android/rockchip/camera2/view/c;"}
    com.android.rockchip.camera2.view.TpImageView$GestureState IDLE -> IDLE
      # {"id":"com.android.tools.r8.residualsignature","signature":"Lcom/android/rockchip/camera2/view/c;"}
    com.android.rockchip.camera2.view.TpImageView$GestureState PANNING -> PANNING
      # {"id":"com.android.tools.r8.residualsignature","signature":"Lcom/android/rockchip/camera2/view/c;"}
    com.android.rockchip.camera2.view.TpImageView$GestureState SCALING -> SCALING
      # {"id":"com.android.tools.r8.residualsignature","signature":"Lcom/android/rockchip/camera2/view/c;"}
    1:1:com.android.rockchip.camera2.view.TpImageView$GestureState[] $values():59:59 -> $values
      # {"id":"com.android.tools.r8.residualsignature","signature":"()[Lcom/android/rockchip/camera2/view/c;"}
    1:3:void <clinit>():60:62 -> <clinit>
    4:4:void <clinit>():59:59 -> <clinit>
    1:1:void <init>(java.lang.String,int):59:59 -> <init>
    1:1:com.android.rockchip.camera2.view.TpImageView$GestureState valueOf(java.lang.String):59:59 -> valueOf
      # {"id":"com.android.tools.r8.residualsignature","signature":"(Ljava/lang/String;)Lcom/android/rockchip/camera2/view/c;"}
    1:1:com.android.rockchip.camera2.view.TpImageView$GestureState[] values():59:59 -> values
      # {"id":"com.android.tools.r8.residualsignature","signature":"()[Lcom/android/rockchip/camera2/view/c;"}
com.android.rockchip.camera2.view.TpImageView$OnZoomChangeListener -> com.android.rockchip.camera2.view.TpImageView$OnZoomChangeListener:
# {"id":"sourceFile","fileName":"TpImageView.java"}
com.android.rockchip.camera2.view.TpImageView$ScaleInfo -> com.android.rockchip.camera2.view.d:
# {"id":"sourceFile","fileName":"TpImageView.java"}
    float currentScale -> a
    float fitScreenScale -> b
    float minScale -> c
    float maxScale -> d
    boolean userSetScaleRange -> e
    1:6:void <init>():68:73 -> <init>
com.android.rockchip.camera2.view.TpRoiView -> com.android.rockchip.camera2.view.TpRoiView:
# {"id":"sourceFile","fileName":"TpRoiView.java"}
    com.android.rockchip.camera2.view.TpRoiView$ControlHandle activeHandle -> activeHandle
      # {"id":"com.android.tools.r8.residualsignature","signature":"Lcom/android/rockchip/camera2/view/g;"}
    1:1:void <init>(android.content.Context):157:157 -> <init>
    2:88:void <init>(android.content.Context):72:158 -> <init>
    89:89:void <init>(android.content.Context,android.util.AttributeSet):169:169 -> <init>
    90:188:void <init>(android.content.Context,android.util.AttributeSet):72:170 -> <init>
    189:189:void <init>(android.content.Context,android.util.AttributeSet,int):182:182 -> <init>
    190:301:void <init>(android.content.Context,android.util.AttributeSet,int):72:183 -> <init>
    1:37:void animateRectAppear():612:648 -> animateRectAppear
    1:33:void animateRectDisappear():667:699 -> animateRectDisappear
    1:2:void applyISPParamUpdate(int,int,int,int):1371:1372 -> applyISPParamUpdate
    1:44:void applyMirrorFlipToROI():436:479 -> applyMirrorFlipToROI
    1:4:void applyTransform(android.graphics.Matrix):1353:1356 -> applyTransform
    1:4:void clearROIParamCache():398:401 -> clearROIParamCache
    1:1:boolean containsPoint(android.graphics.RectF,float,float):1069:1069 -> containsPoint
    1:4:void drawControlHandles(android.graphics.Canvas,android.graphics.RectF):1211:1214 -> drawControlHandles
    1:3:void drawHandleCircles(android.graphics.Canvas,float,float):1227:1229 -> drawHandleCircles
    1:24:com.android.rockchip.camera2.view.TpRoiView$ControlHandle getHandleAtPoint(float,float):959:982 -> getHandleAtPoint
      # {"id":"com.android.tools.r8.residualsignature","signature":"(FF)Lcom/android/rockchip/camera2/view/g;"}
    1:32:void init(android.content.Context):194:225 -> init
    1:5:void initAnimator():488:492 -> initAnimator
    1:23:void initializeROIRect():721:743 -> initializeROIRect
    1:1:boolean isPointNearHandle(float,float,float,float):999:999 -> isPointNearHandle
    1:1:boolean isROIEnabled():709:709 -> isROIEnabled
    1:16:void lambda$animateRectAppear$2(float,android.graphics.RectF,float,android.animation.ValueAnimator):626:641 -> lambda$animateRectAppear$2
    1:16:void lambda$animateRectDisappear$3(android.graphics.RectF,float,float,android.animation.ValueAnimator):680:695 -> lambda$animateRectDisappear$3
    1:16:void lambda$initAnimator$1(android.animation.ValueAnimator):494:509 -> lambda$initAnimator$1
    1:1:void lambda$notifyROIChanged$4():1193:1193 -> lambda$notifyROIChanged$4
    1:3:void lambda$updateROIFromParams$0():385:387 -> lambda$updateROIFromParams$0
    1:8:void loadMirrorFlipState():233:240 -> loadMirrorFlipState
    1:21:boolean loadROIParamsFromIsp():1311:1331 -> loadROIParamsFromIsp
    1:7:float[] mapPoint(float,float):1392:1398 -> mapPoint
    1:50:void moveRect(float,float):1081:1130 -> moveRect
    1:55:void notifyROIChanged():1142:1196 -> notifyROIChanged
    1:43:void onDataChanged(com.android.rockchip.camera2.util.TouptekIspParam,int):255:297 -> onDataChanged
    44:45:void onDataChanged(com.android.rockchip.camera2.util.TouptekIspParam,int):292:293 -> onDataChanged
    46:47:void onDataChanged(com.android.rockchip.camera2.util.TouptekIspParam,int):288:289 -> onDataChanged
    48:74:void onDataChanged(com.android.rockchip.camera2.util.TouptekIspParam,int):284:310 -> onDataChanged
    1:4:void onDetachedFromWindow():422:425 -> onDetachedFromWindow
    1:34:void onDraw(android.graphics.Canvas):775:808 -> onDraw
    35:46:void onDraw(android.graphics.Canvas):807:818 -> onDraw
    47:53:void onDraw(android.graphics.Canvas):817:823 -> onDraw
    1:7:void onSizeChanged(int,int,int,int):757:763 -> onSizeChanged
    1:108:boolean onTouchEvent(android.view.MotionEvent):835:942 -> onTouchEvent
    109:202:boolean onTouchEvent(android.view.MotionEvent):853:946 -> onTouchEvent
    1:2:void resetTransforms():1379:1380 -> resetTransforms
    1:22:void resizeRectByHandle(float,float):1013:1034 -> resizeRectByHandle
    23:24:void resizeRectByHandle(float,float):1028:1029 -> resizeRectByHandle
    25:26:void resizeRectByHandle(float,float):1023:1024 -> resizeRectByHandle
    27:65:void resizeRectByHandle(float,float):1018:1056 -> resizeRectByHandle
    1:2:void setCameraResolution(int,int):556:557 -> setCameraResolution
    1:33:void setROIEnabled(boolean):567:599 -> setROIEnabled
    1:56:void setROIParams(int,int,int,int):1242:1297 -> setROIParams
    1:5:float[] unmapPoint(float,float):1413:1417 -> unmapPoint
    1:23:void updateOriginalRect():523:545 -> updateOriginalRect
    1:63:void updateROIFromParams(int,int,int,int):328:390 -> updateROIFromParams
com.android.rockchip.camera2.view.TpRoiView$1 -> com.android.rockchip.camera2.view.e:
# {"id":"sourceFile","fileName":"TpRoiView.java"}
    com.android.rockchip.camera2.view.TpRoiView this$0 -> a
    1:1:void <init>(com.android.rockchip.camera2.view.TpRoiView):649:649 -> <init>
    1:1:void onAnimationEnd(android.animation.Animator):653:653 -> onAnimationEnd
com.android.rockchip.camera2.view.TpRoiView$2 -> com.android.rockchip.camera2.view.f:
# {"id":"sourceFile","fileName":"TpRoiView.java"}
    int[] $SwitchMap$com$android$rockchip$camera2$util$TouptekIspParam -> a
    int[] $SwitchMap$com$android$rockchip$camera2$view$TpRoiView$ControlHandle -> b
    1:1:void <clinit>():1015:1015 -> <clinit>
    2:2:void <clinit>():281:281 -> <clinit>
com.android.rockchip.camera2.view.TpRoiView$ControlHandle -> com.android.rockchip.camera2.view.g:
# {"id":"sourceFile","fileName":"TpRoiView.java"}
    com.android.rockchip.camera2.view.TpRoiView$ControlHandle[] $VALUES -> $VALUES
      # {"id":"com.android.tools.r8.residualsignature","signature":"[Lcom/android/rockchip/camera2/view/g;"}
    com.android.rockchip.camera2.view.TpRoiView$ControlHandle BOTTOM_LEFT -> BOTTOM_LEFT
      # {"id":"com.android.tools.r8.residualsignature","signature":"Lcom/android/rockchip/camera2/view/g;"}
    com.android.rockchip.camera2.view.TpRoiView$ControlHandle BOTTOM_RIGHT -> BOTTOM_RIGHT
      # {"id":"com.android.tools.r8.residualsignature","signature":"Lcom/android/rockchip/camera2/view/g;"}
    com.android.rockchip.camera2.view.TpRoiView$ControlHandle NONE -> NONE
      # {"id":"com.android.tools.r8.residualsignature","signature":"Lcom/android/rockchip/camera2/view/g;"}
    com.android.rockchip.camera2.view.TpRoiView$ControlHandle TOP_LEFT -> TOP_LEFT
      # {"id":"com.android.tools.r8.residualsignature","signature":"Lcom/android/rockchip/camera2/view/g;"}
    com.android.rockchip.camera2.view.TpRoiView$ControlHandle TOP_RIGHT -> TOP_RIGHT
      # {"id":"com.android.tools.r8.residualsignature","signature":"Lcom/android/rockchip/camera2/view/g;"}
    1:1:com.android.rockchip.camera2.view.TpRoiView$ControlHandle[] $values():136:136 -> $values
      # {"id":"com.android.tools.r8.residualsignature","signature":"()[Lcom/android/rockchip/camera2/view/g;"}
    1:9:void <clinit>():139:147 -> <clinit>
    10:10:void <clinit>():136:136 -> <clinit>
    1:1:void <init>(java.lang.String,int):136:136 -> <init>
    1:1:com.android.rockchip.camera2.view.TpRoiView$ControlHandle valueOf(java.lang.String):136:136 -> valueOf
      # {"id":"com.android.tools.r8.residualsignature","signature":"(Ljava/lang/String;)Lcom/android/rockchip/camera2/view/g;"}
    1:1:com.android.rockchip.camera2.view.TpRoiView$ControlHandle[] values():136:136 -> values
      # {"id":"com.android.tools.r8.residualsignature","signature":"()[Lcom/android/rockchip/camera2/view/g;"}
com.android.rockchip.camera2.view.TpTextureView -> com.android.rockchip.camera2.view.TpTextureView:
# {"id":"sourceFile","fileName":"TpTextureView.java"}
    com.android.rockchip.camera2.view.TpTextureView$GestureState mGestureState -> mGestureState
      # {"id":"com.android.tools.r8.residualsignature","signature":"Lcom/android/rockchip/camera2/view/j;"}
    com.android.rockchip.camera2.view.TpTextureView$ScaleInfo mScaleInfo -> mScaleInfo
      # {"id":"com.android.tools.r8.residualsignature","signature":"Lcom/android/rockchip/camera2/view/k;"}
    1:1:void <init>(android.content.Context):147:147 -> <init>
    2:37:void <init>(android.content.Context):113:148 -> <init>
    38:38:void <init>(android.content.Context,android.util.AttributeSet):152:152 -> <init>
    39:79:void <init>(android.content.Context,android.util.AttributeSet):113:153 -> <init>
    80:80:void <init>(android.content.Context,android.util.AttributeSet,int):157:157 -> <init>
    81:126:void <init>(android.content.Context,android.util.AttributeSet,int):113:158 -> <init>
    1:3:void cancelResetGestureState():418:420 -> cancelResetGestureState
    1:32:android.view.ScaleGestureDetector createOptimizedScaleGestureDetector(android.content.Context):225:256 -> createOptimizedScaleGestureDetector
    1:5:float getCurrentScale():394:398 -> getCurrentScale
    1:1:float getMaxScale():527:527 -> getMaxScale
    1:1:com.android.rockchip.camera2.view.TpTextureView$TouchEventHandler getTouchEventHandler():497:497 -> getTouchEventHandler
    1:38:boolean handlePanGesture(android.view.MotionEvent):292:329 -> handlePanGesture
    39:51:boolean handlePanGesture(android.view.MotionEvent):295:307 -> handlePanGesture
    1:1:void init(android.content.Context):166:166 -> init
    1:4:void initGestureDetectors(android.content.Context):176:179 -> initGestureDetectors
    1:1:boolean isDoubleTapEnabled():518:518 -> isDoubleTapEnabled
    1:1:boolean isPanEnabled():511:511 -> isPanEnabled
    1:1:boolean isZoomEnabled():504:504 -> isZoomEnabled
    1:2:void lambda$scheduleResetGestureState$0():409:410 -> lambda$scheduleResetGestureState$0
    1:15:boolean onTouchEvent(android.view.MotionEvent):270:284 -> onTouchEvent
    1:30:boolean performScale(float,float,float):340:369 -> performScale
    1:10:boolean performTranslate(float,float):377:386 -> performTranslate
    1:8:void scheduleResetGestureState():407:414 -> scheduleResetGestureState
    1:2:void setDoubleTapEnabled(boolean):464:465 -> setDoubleTapEnabled
    1:1:void setMaxScale(float):437:437 -> setMaxScale
    2:2:void com.android.rockchip.camera2.view.TpTextureView$ScaleInfo.setUserMaxScale(float):102:102 -> setMaxScale
    2:2:void setMaxScale(float):437 -> setMaxScale
      # {"id":"com.android.tools.r8.rewriteFrame","conditions":["throws(Ljava/lang/NullPointerException;)"],"actions":["removeInnerFrames(1)"]}
    3:3:void setMaxScale(float):438:438 -> setMaxScale
    1:1:void setOnZoomChangeListener(com.android.rockchip.camera2.view.TpTextureView$OnZoomChangeListener):480:480 -> setOnZoomChangeListener
    1:2:void setPanEnabled(boolean):456:457 -> setPanEnabled
    1:2:void setRoiView(com.android.rockchip.camera2.view.TpRoiView):472:473 -> setRoiView
    1:2:void setTouchEventHandler(com.android.rockchip.camera2.view.TpTextureView$TouchEventHandler):488:489 -> setTouchEventHandler
    1:2:void setZoomEnabled(boolean):448:449 -> setZoomEnabled
com.android.rockchip.camera2.view.TpTextureView$1 -> com.android.rockchip.camera2.view.h:
# {"id":"sourceFile","fileName":"TpTextureView.java"}
    com.android.rockchip.camera2.view.TpTextureView this$0 -> a
    1:1:void <init>(com.android.rockchip.camera2.view.TpTextureView):179:179 -> <init>
    1:12:boolean onDoubleTap(android.view.MotionEvent):182:193 -> onDoubleTap
    1:2:void onLongPress(android.view.MotionEvent):212:213 -> onLongPress
    1:5:boolean onSingleTapConfirmed(android.view.MotionEvent):201:205 -> onSingleTapConfirmed
com.android.rockchip.camera2.view.TpTextureView$2 -> com.android.rockchip.camera2.view.i:
# {"id":"sourceFile","fileName":"TpTextureView.java"}
    com.android.rockchip.camera2.view.TpTextureView this$0 -> a
    1:1:void <init>(com.android.rockchip.camera2.view.TpTextureView):225:225 -> <init>
    1:7:boolean onScale(android.view.ScaleGestureDetector):228:234 -> onScale
    1:2:boolean onScaleBegin(android.view.ScaleGestureDetector):239:240 -> onScaleBegin
    1:1:void onScaleEnd(android.view.ScaleGestureDetector):247:247 -> onScaleEnd
com.android.rockchip.camera2.view.TpTextureView$GestureState -> com.android.rockchip.camera2.view.j:
# {"id":"sourceFile","fileName":"TpTextureView.java"}
    com.android.rockchip.camera2.view.TpTextureView$GestureState[] $VALUES -> $VALUES
      # {"id":"com.android.tools.r8.residualsignature","signature":"[Lcom/android/rockchip/camera2/view/j;"}
    com.android.rockchip.camera2.view.TpTextureView$GestureState IDLE -> IDLE
      # {"id":"com.android.tools.r8.residualsignature","signature":"Lcom/android/rockchip/camera2/view/j;"}
    com.android.rockchip.camera2.view.TpTextureView$GestureState PANNING -> PANNING
      # {"id":"com.android.tools.r8.residualsignature","signature":"Lcom/android/rockchip/camera2/view/j;"}
    com.android.rockchip.camera2.view.TpTextureView$GestureState SCALING -> SCALING
      # {"id":"com.android.tools.r8.residualsignature","signature":"Lcom/android/rockchip/camera2/view/j;"}
    1:1:com.android.rockchip.camera2.view.TpTextureView$GestureState[] $values():55:55 -> $values
      # {"id":"com.android.tools.r8.residualsignature","signature":"()[Lcom/android/rockchip/camera2/view/j;"}
    1:3:void <clinit>():56:58 -> <clinit>
    4:4:void <clinit>():55:55 -> <clinit>
    1:1:void <init>(java.lang.String,int):55:55 -> <init>
    1:1:com.android.rockchip.camera2.view.TpTextureView$GestureState valueOf(java.lang.String):55:55 -> valueOf
      # {"id":"com.android.tools.r8.residualsignature","signature":"(Ljava/lang/String;)Lcom/android/rockchip/camera2/view/j;"}
    1:1:com.android.rockchip.camera2.view.TpTextureView$GestureState[] values():55:55 -> values
      # {"id":"com.android.tools.r8.residualsignature","signature":"()[Lcom/android/rockchip/camera2/view/j;"}
com.android.rockchip.camera2.view.TpTextureView$OnZoomChangeListener -> com.android.rockchip.camera2.view.TpTextureView$OnZoomChangeListener:
# {"id":"sourceFile","fileName":"TpTextureView.java"}
com.android.rockchip.camera2.view.TpTextureView$ScaleInfo -> com.android.rockchip.camera2.view.k:
# {"id":"sourceFile","fileName":"TpTextureView.java"}
    float currentScale -> a
    float maxScale -> b
    1:3:void <init>():96:98 -> <init>
com.android.rockchip.camera2.view.TpTextureView$TouchEventHandler -> com.android.rockchip.camera2.view.TpTextureView$TouchEventHandler:
# {"id":"sourceFile","fileName":"TpTextureView.java"}
com.android.rockchip.camera2.view.TpVideoPlayerView -> com.android.rockchip.camera2.view.TpVideoPlayerView:
# {"id":"sourceFile","fileName":"TpVideoPlayerView.java"}
    1:2:void <clinit>():80:81 -> <clinit>
    1:1:void <init>(android.content.Context):137:137 -> <init>
    2:2:void <init>(android.content.Context,android.util.AttributeSet):141:141 -> <init>
    3:3:void <init>(android.content.Context,android.util.AttributeSet,int):145:145 -> <init>
    4:53:void <init>(android.content.Context,android.util.AttributeSet,int):99:148 -> <init>
    1:1:void cancelHideControls():1362:1362 -> cancelHideControls
    1:19:void fastForward(int):1188:1206 -> fastForward
    1:15:void fastRewind(int):1218:1232 -> fastRewind
    1:1:void forceUpdateProgressToEnd(long):1265:1265 -> forceUpdateProgressToEnd
    1:13:java.lang.String formatTime(long):721:733 -> formatTime
    1:1:long getCurrentPosition():1080:1080 -> getCurrentPosition
    1:1:long getDuration():1089:1089 -> getDuration
    1:1:float getPlaybackSpeed():1119:1119 -> getPlaybackSpeed
    1:2:int getSpeedIndex(float):614:615 -> getSpeedIndex
    1:1:com.android.rockchip.camera2.view.TpTextureView getTextureView():1392:1392 -> getTextureView
    1:1:com.android.rockchip.camera2.view.TpVideoPlayerView$VideoPlayerListener getVideoPlayerListener():1435:1435 -> getVideoPlayerListener
    1:1:com.android.rockchip.camera2.video.TpVideoSystem getVideoSystem():1383:1383 -> getVideoSystem
    1:13:void hideControls():1324:1336 -> hideControls
    1:13:void initComponents():169:181 -> initComponents
    1:33:void initControlButtons():270:302 -> initControlButtons
    1:8:void initControlsAutoHide():256:263 -> initControlsAutoHide
    1:22:void initControlsView():188:209 -> initControlsView
    1:5:void initFromAttributes(android.util.AttributeSet):156:160 -> initFromAttributes
    1:1:boolean isControlsVisible():1303:1303 -> isControlsVisible
    1:1:boolean isPlaybackCompleted():1161:1161 -> isPlaybackCompleted
    1:1:boolean isPlaying():1071:1071 -> isPlaying
    1:8:void lambda$forceUpdateProgressToEnd$20(long):1268:1275 -> lambda$forceUpdateProgressToEnd$20
    1:2:void lambda$hideControls$21():1330:1331 -> lambda$hideControls$21
    1:2:void lambda$initControlsAutoHide$0():257:258 -> lambda$initControlsAutoHide$0
    1:1:void lambda$pause$17():1030:1030 -> lambda$pause$17
    1:4:void lambda$play$15():995:998 -> lambda$play$15
    1:4:void lambda$play$16():1008:1011 -> lambda$play$16
    1:2:void lambda$recreateSurface$14():870:871 -> lambda$recreateSurface$14
    1:1:void lambda$setupControlListeners$1():319:319 -> lambda$setupControlListeners$1
    1:8:void lambda$setupControlListeners$2(android.view.View):313:320 -> lambda$setupControlListeners$2
    1:2:void lambda$setupControlListeners$3(android.view.View):327:328 -> lambda$setupControlListeners$3
    1:2:void lambda$setupControlListeners$4(android.view.View):335:336 -> lambda$setupControlListeners$4
    1:2:void lambda$setupControlListeners$5(android.view.View):343:344 -> lambda$setupControlListeners$5
    1:2:void lambda$setupControlListeners$6(android.view.View):351:352 -> lambda$setupControlListeners$6
    1:2:void lambda$setupControlListeners$7(android.view.View):359:360 -> lambda$setupControlListeners$7
    1:2:void lambda$setupControlListeners$8(android.view.View):367:368 -> lambda$setupControlListeners$8
    1:3:boolean lambda$setupControlsInteractionDetection$9(android.view.View,android.view.MotionEvent):419:421 -> lambda$setupControlsInteractionDetection$9
    1:2:void lambda$showSpeedSelectionMenu$11(android.widget.PopupWindow):556:557 -> lambda$showSpeedSelectionMenu$11
    1:10:void lambda$showSpeedSelectionMenu$12(android.view.View,android.widget.PopupWindow):550:559 -> lambda$showSpeedSelectionMenu$12
    1:23:void lambda$showSpeedSelectionMenu$13(float,android.widget.TextView[],android.widget.PopupWindow,android.view.View):539:561 -> lambda$showSpeedSelectionMenu$13
    1:3:void lambda$stop$18():1047:1049 -> lambda$stop$18
    1:6:void lambda$updatePlayPauseButtonIcon$10(android.widget.ImageButton,int):458:463 -> lambda$updatePlayPauseButtonIcon$10
    1:14:void lambda$updateProgressAndTime$19():1242:1255 -> lambda$updateProgressAndTime$19
    1:57:void loadVideo():919:975 -> loadVideo
    1:2:void nextVideo():491:492 -> nextVideo
    1:4:void onDetachedFromWindow():1397:1400 -> onDetachedFromWindow
    1:7:void pause():1025:1031 -> pause
    1:35:void play():983:1017 -> play
    1:2:void previousVideo():480:481 -> previousVideo
    1:16:void recreateSurface():854:869 -> recreateSurface
    1:6:void releaseVideoSystem():889:894 -> releaseVideoSystem
    1:2:void resetControlsTimer():1369:1370 -> resetControlsTimer
    1:1:void resetToStart():1171:1171 -> resetToStart
    1:2:void scheduleHideControls():1354:1355 -> scheduleHideControls
    1:3:void seekRelative(long):1146:1148 -> seekRelative
    1:2:void seekTo(long):1061:1062 -> seekTo
    1:7:void setPlaybackSpeed(float):1103:1109 -> setPlaybackSpeed
    1:5:void setShowControls(boolean):1289:1293 -> setShowControls
    1:6:void setVideoPath(java.lang.String):906:911 -> setVideoPath
    1:1:void setVideoPlayerListener(com.android.rockchip.camera2.view.TpVideoPlayerView$VideoPlayerListener):1426:1426 -> setVideoPlayerListener
    1:99:void setupControlListeners():311:409 -> setupControlListeners
    1:3:void setupControlsInteractionDetection():416:418 -> setupControlsInteractionDetection
    1:1:void setupTouchEventHandler():217:217 -> setupTouchEventHandler
    1:54:void setupVideoSystem():742:795 -> setupVideoSystem
    1:6:void showControls():1312:1317 -> showControls
    1:73:void showSpeedSelectionMenu():502:574 -> showSpeedSelectionMenu
    74:87:void showSpeedSelectionMenu():572:585 -> showSpeedSelectionMenu
    1:13:void startProgressUpdater():628:640 -> startProgressUpdater
    1:14:void startStatusSync():658:671 -> startStatusSync
    1:1:void stepFrame():1129:1129 -> stepFrame
    1:8:void stop():1039:1046 -> stop
    1:4:void stopProgressUpdater():647:650 -> stopProgressUpdater
    1:4:void stopStatusSync():679:682 -> stopStatusSync
    1:4:void toggleControlsVisibility():1343:1346 -> toggleControlsVisibility
    1:32:void updatePlayPauseButtonIcon(boolean):437:468 -> updatePlayPauseButtonIcon
    1:7:void updateProgress():691:697 -> updateProgress
    1:1:void updateProgressAndTime():1241:1241 -> updateProgressAndTime
    1:11:void updateSpeedSelectionDisplay(android.widget.TextView[],float[],java.lang.String[]):595:605 -> updateSpeedSelectionDisplay
    1:9:void updateTimeDisplay(long,long):705:713 -> updateTimeDisplay
com.android.rockchip.camera2.view.TpVideoPlayerView$1 -> com.android.rockchip.camera2.view.TpVideoPlayerView$1:
# {"id":"sourceFile","fileName":"TpVideoPlayerView.java"}
    1:1:void <init>(com.android.rockchip.camera2.view.TpVideoPlayerView):217:217 -> <init>
    1:2:void onLongPressDetected(android.view.MotionEvent):230:231 -> onLongPressDetected
    1:2:boolean onScaleGestureDetected(android.view.MotionEvent):243:244 -> onScaleGestureDetected
    1:4:boolean onSingleTapDetected(android.view.MotionEvent):220:223 -> onSingleTapDetected
com.android.rockchip.camera2.view.TpVideoPlayerView$2 -> com.android.rockchip.camera2.view.TpVideoPlayerView$2:
# {"id":"sourceFile","fileName":"TpVideoPlayerView.java"}
    1:1:void <init>(com.android.rockchip.camera2.view.TpVideoPlayerView):374:374 -> <init>
    1:4:void onProgressChanged(com.android.rockchip.camera2.view.TpCustomProgressBar,int,boolean):379:382 -> onProgressChanged
    1:2:void onStartTrackingTouch(com.android.rockchip.camera2.view.TpCustomProgressBar):389:390 -> onStartTrackingTouch
    1:8:void onStopTrackingTouch(com.android.rockchip.camera2.view.TpCustomProgressBar):395:402 -> onStopTrackingTouch
com.android.rockchip.camera2.view.TpVideoPlayerView$3 -> com.android.rockchip.camera2.view.l:
# {"id":"sourceFile","fileName":"TpVideoPlayerView.java"}
    com.android.rockchip.camera2.view.TpVideoPlayerView this$0 -> a
    1:1:void <init>(com.android.rockchip.camera2.view.TpVideoPlayerView):631:631 -> <init>
    1:3:void run():634:636 -> run
com.android.rockchip.camera2.view.TpVideoPlayerView$4 -> com.android.rockchip.camera2.view.m:
# {"id":"sourceFile","fileName":"TpVideoPlayerView.java"}
    com.android.rockchip.camera2.view.TpVideoPlayerView this$0 -> a
    1:1:void <init>(com.android.rockchip.camera2.view.TpVideoPlayerView):661:661 -> <init>
    1:4:void run():664:667 -> run
com.android.rockchip.camera2.view.TpVideoPlayerView$5 -> com.android.rockchip.camera2.view.n:
# {"id":"sourceFile","fileName":"TpVideoPlayerView.java"}
    com.android.rockchip.camera2.view.TpVideoPlayerView this$0 -> a
    1:1:void <init>(com.android.rockchip.camera2.view.TpVideoPlayerView):746:746 -> <init>
    1:16:void onSurfaceTextureAvailable(android.graphics.SurfaceTexture,int,int):749:764 -> onSurfaceTextureAvailable
    1:9:boolean onSurfaceTextureDestroyed(android.graphics.SurfaceTexture):776:784 -> onSurfaceTextureDestroyed
    1:1:void onSurfaceTextureSizeChanged(android.graphics.SurfaceTexture,int,int):770:770 -> onSurfaceTextureSizeChanged
com.android.rockchip.camera2.view.TpVideoPlayerView$6 -> com.android.rockchip.camera2.view.TpVideoPlayerView$6:
# {"id":"sourceFile","fileName":"TpVideoPlayerView.java"}
    1:1:void <init>(com.android.rockchip.camera2.view.TpVideoPlayerView):795:795 -> <init>
    1:2:void lambda$onError$3():829:830 -> lambda$onError$3
    1:1:void lambda$onError$4():837:837 -> lambda$onError$4
    1:1:void lambda$onVideoPlaybackCompleted$1():807:807 -> lambda$onVideoPlaybackCompleted$1
    1:1:void lambda$onVideoPlaybackStarted$0():800:800 -> lambda$onVideoPlaybackStarted$0
    1:1:void lambda$onVideoPlaybackStopped$2():814:814 -> lambda$onVideoPlaybackStopped$2
    1:14:void onError(java.lang.String):820:833 -> onError
    15:24:void onError(java.lang.String):828:837 -> onError
    1:3:void onVideoPlaybackCompleted(java.lang.String):805:807 -> onVideoPlaybackCompleted
    1:3:void onVideoPlaybackStarted(java.lang.String):798:800 -> onVideoPlaybackStarted
    1:1:void onVideoPlaybackStopped():814:814 -> onVideoPlaybackStopped
com.android.rockchip.camera2.view.TpVideoPlayerView$VideoPlayerListener -> com.android.rockchip.camera2.view.TpVideoPlayerView$VideoPlayerListener:
# {"id":"sourceFile","fileName":"TpVideoPlayerView.java"}
