-- Merging decision tree log ---
manifest
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:2:1-89:12
INJECTED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:2:1-89:12
INJECTED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:2:1-89:12
INJECTED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:2:1-89:12
MERGED from [TP2HD-VisionSDK.aar] C:\Users\<USER>\.gradle\caches\transforms-4\3ccc4450160497872e02edabc009ecfb\transformed\TP2HD-VisionSDK\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\bc8aa82520e8dbb6d69b8567e459d316\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\080256d1eebbfd88f7470acbe2400055\transformed\constraintlayout-2.2.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\e3d9a1fcbdf0d6a6e4510e72999cfb07\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\63d9a320484d40551e334698c8aad5a1\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-4\7ab7586f782e1dfaf77998583739c3df\transformed\glide-4.15.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ddcb1ff1872ec5c605d81bc7bf4df707\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\d28c72f89ae3b193aba1ffbddc0ccc99\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\3eff0408d770257651ac0655ad8bf002\transformed\activity-1.10.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\3bde3060b7ab0f6cb97bcfe13b8032cc\transformed\recyclerview-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1708c35514dec8ed8eb018c487cc03fa\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.pedroSG94:RTSP-Server:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\4d79b6d4d4295ef1568b5bc1326c2a8b\transformed\RTSP-Server-1.2.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.pedroSG94.RootEncoder:library:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\edc91c129b6f26810f57bfdc275dfe97\transformed\library-2.3.5\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.beyka:Android-TiffBitmapFactory:*******] C:\Users\<USER>\.gradle\caches\transforms-4\a9b07e61d85a8265fe24df7d55359a0e\transformed\Android-TiffBitmapFactory-*******\AndroidManifest.xml:2:1-14:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1bb32382be12bddb34483d2d06f1cb61\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\14773883bdf56808997dc83a9daf4940\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b53131b5458fe3881864d227138d78a2\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a6ffe7991f68b5934341f0e5951ddb6\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\93159e3bb7db567d42a4ec2620aa6e2b\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\dc51b6fd7f43d251d9c379621cbf774d\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\88a328a3529cc848c36e857e65ee451b\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d377f557203330d40b2d2db0ec3a5761\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\745ebaa56a37d14dd80824f7313a0750\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\41671412a7ea8f88fd6869bf552aea66\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\7d554d83977f652d4a02b1eb4eef1adb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\9306ec361073d5fc0d3e09d69f866e6f\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\a7e7c2966fb6aefa3b8b213275feec18\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\0d9f5962a81b4cf5303e0a56a1893241\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\d3b9594318906c3194d9b915895fff59\transformed\core-ktx-1.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\e259b1e507e6128237302f34029d1c26\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c1a1084f1edd9df35d5af90fcb0c9f0\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\d31cab8c5e76a7f069e99ecc998d7554\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\3f337421a01a4d4f98be4377e3140ab8\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a03e9269f3e4a35c8e91891120a4c47\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\ca0b77dfec151509a5288f6b0fefffa8\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c9fb506d1bb02ca98280e33e28d9b38\transformed\core-1.13.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\46991202cb4e64dbcf66035a06968101\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\d17a7b93296c4e904c81c859d3d35a8e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\150fb8ca893ce7c77fec935909a6b8ce\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\233bd232f99c0a8bddbdbda7953b7420\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-4\a96ab53f07d1b8523325502943be50c7\transformed\gifdecoder-4.15.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.exifinterface:exifinterface:1.3.3] C:\Users\<USER>\.gradle\caches\transforms-4\1685f1d4f4e579e8a3208f900844dce6\transformed\exifinterface-1.3.3\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.pedroSG94.RootEncoder:encoder:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\7e88f7c1147c774d1e0810447c002a13\transformed\encoder-2.3.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7ae094211e49000700370b1c27573f88\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\0c6ef4f1c2196c717ff61866694eb142\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\254e0728763919256b38003ddd64de89\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab17ed870356635c7759a4b7d6b78f22\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fb9b41813e9f5ff7e2db705f8eda4bcf\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ffd6c0d30963042505ec9a438f5048eb\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.pedroSG94.RootEncoder:rtmp:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\217b26216412ab3a46ca416c64b548fe\transformed\rtmp-2.3.5\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.pedroSG94.RootEncoder:rtsp:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\79255cdaee0505193517c38db20f1bef\transformed\rtsp-2.3.5\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.pedroSG94.RootEncoder:srt:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\ea6ffb5dcddd5ac8e0a5a575f6ff726a\transformed\srt-2.3.5\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.pedroSG94.RootEncoder:common:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\256e04cb5415f9d9c05b816f873a16e3\transformed\common-2.3.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\1a3512344cb2d58e2558806fd3b5e1f1\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml
	android:sharedUserId
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:3:5-46
	android:versionName
		INJECTED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:4:5-51
	android:versionCode
		INJECTED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.CAMERA
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:7:5-65
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:7:22-62
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:9:5-81
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:9:22-78
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:11:5-71
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:11:22-68
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:12:5-13:38
	android:maxSdkVersion
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:13:9-35
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:12:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:14:5-79
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:14:22-77
uses-permission#android.permission.INTERNET
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:16:5-67
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:16:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:17:5-79
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:17:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:25:5-75
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:25:22-73
uses-permission#android.permission.CHANGE_WIFI_STATE
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:26:5-75
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:26:22-73
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:27:5-78
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:27:22-76
uses-permission#android.permission.TETHER_PRIVILEGED
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:28:5-75
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:28:22-73
uses-permission#android.permission.WRITE_SETTINGS
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:29:5-73
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:29:22-70
uses-permission#android.permission.MANAGE_WIFI_HOTSPOT
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:30:5-77
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:30:22-75
uses-permission#android.permission.CONNECTIVITY_INTERNAL
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:31:5-115
	tools:ignore
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:31:78-113
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:31:22-77
uses-permission#android.permission.START_TETHERING
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:32:5-109
	tools:ignore
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:32:72-107
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:32:22-71
uses-permission#com.android.providers.tv.permission.READ_EPG_DATA
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:35:5-89
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:35:22-86
uses-feature#android.software.live_tv
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:38:5-86
	android:required
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:38:59-83
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:38:19-58
uses-permission#android.permission.NEARBY_WIFI_DEVICES
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:41:5-78
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:41:22-75
uses-feature#android.hardware.camera
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:44:5-60
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:44:19-57
application
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:45:5-86:19
INJECTED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:45:5-86:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\bc8aa82520e8dbb6d69b8567e459d316\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\bc8aa82520e8dbb6d69b8567e459d316\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\080256d1eebbfd88f7470acbe2400055\transformed\constraintlayout-2.2.0\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\080256d1eebbfd88f7470acbe2400055\transformed\constraintlayout-2.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-4\7ab7586f782e1dfaf77998583739c3df\transformed\glide-4.15.1\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-4\7ab7586f782e1dfaf77998583739c3df\transformed\glide-4.15.1\AndroidManifest.xml:9:5-20
MERGED from [io.github.beyka:Android-TiffBitmapFactory:*******] C:\Users\<USER>\.gradle\caches\transforms-4\a9b07e61d85a8265fe24df7d55359a0e\transformed\Android-TiffBitmapFactory-*******\AndroidManifest.xml:9:5-12:19
MERGED from [io.github.beyka:Android-TiffBitmapFactory:*******] C:\Users\<USER>\.gradle\caches\transforms-4\a9b07e61d85a8265fe24df7d55359a0e\transformed\Android-TiffBitmapFactory-*******\AndroidManifest.xml:9:5-12:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a6ffe7991f68b5934341f0e5951ddb6\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a6ffe7991f68b5934341f0e5951ddb6\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\7d554d83977f652d4a02b1eb4eef1adb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\7d554d83977f652d4a02b1eb4eef1adb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c9fb506d1bb02ca98280e33e28d9b38\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c9fb506d1bb02ca98280e33e28d9b38\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\d17a7b93296c4e904c81c859d3d35a8e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\d17a7b93296c4e904c81c859d3d35a8e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\150fb8ca893ce7c77fec935909a6b8ce\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\150fb8ca893ce7c77fec935909a6b8ce\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-4\a96ab53f07d1b8523325502943be50c7\transformed\gifdecoder-4.15.1\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-4\a96ab53f07d1b8523325502943be50c7\transformed\gifdecoder-4.15.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\0c6ef4f1c2196c717ff61866694eb142\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\0c6ef4f1c2196c717ff61866694eb142\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c9fb506d1bb02ca98280e33e28d9b38\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:53:9-35
	android:label
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:51:9-41
	android:fullBackupContent
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:48:9-54
	android:roundIcon
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:52:9-54
	tools:targetApi
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:55:9-29
	android:icon
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:50:9-43
	android:allowBackup
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:46:9-35
		REJECTED from [io.github.beyka:Android-TiffBitmapFactory:*******] C:\Users\<USER>\.gradle\caches\transforms-4\a9b07e61d85a8265fe24df7d55359a0e\transformed\Android-TiffBitmapFactory-*******\AndroidManifest.xml:10:9-36
	android:theme
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:54:9-51
	android:dataExtractionRules
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:47:9-65
	tools:replace
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:49:9-44
activity#com.android.rockchip.camera2.integrated.MainActivity
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:57:9-65:20
	android:exported
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:59:13-36
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:58:13-80
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:60:13-64:29
action#android.intent.action.MAIN
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:61:17-69
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:61:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:63:17-77
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:63:27-74
activity#com.android.rockchip.camera2.separated.VideoDecoderActivity
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:67:9-68:48
	tools:ignore
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:68:13-45
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:67:19-93
activity#com.android.rockchip.camera2.separated.MediaBrowserActivity
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:70:9-96
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:70:19-93
activity#com.android.rockchip.camera2.separated.ImageViewerActivity
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:72:9-95
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:72:19-92
activity#com.android.rockchip.camera2.integrated.browser.MediaBrowserActivity
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:75:9-105
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:75:19-102
activity#com.android.rockchip.camera2.integrated.browser.ImageViewerActivity
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:76:9-104
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:76:19-101
activity#com.android.rockchip.camera2.integrated.browser.TpVideoPlayerActivity
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:77:9-106
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:77:19-103
activity#com.android.rockchip.camera2.separated.TpVideoPlayerActivity
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:78:9-97
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:78:19-94
service#com.touptek.video.internal.rtsp.service.RTSPService
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:80:9-85:44
	android:enabled
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:82:13-35
	android:exported
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:83:13-37
	android:stopWithTask
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:85:13-41
	android:foregroundServiceType
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:84:13-60
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:81:13-79
uses-sdk
INJECTED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml
INJECTED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml
MERGED from [TP2HD-VisionSDK.aar] C:\Users\<USER>\.gradle\caches\transforms-4\3ccc4450160497872e02edabc009ecfb\transformed\TP2HD-VisionSDK\AndroidManifest.xml:5:5-44
MERGED from [TP2HD-VisionSDK.aar] C:\Users\<USER>\.gradle\caches\transforms-4\3ccc4450160497872e02edabc009ecfb\transformed\TP2HD-VisionSDK\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\bc8aa82520e8dbb6d69b8567e459d316\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\bc8aa82520e8dbb6d69b8567e459d316\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\080256d1eebbfd88f7470acbe2400055\transformed\constraintlayout-2.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\080256d1eebbfd88f7470acbe2400055\transformed\constraintlayout-2.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\e3d9a1fcbdf0d6a6e4510e72999cfb07\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\e3d9a1fcbdf0d6a6e4510e72999cfb07\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\63d9a320484d40551e334698c8aad5a1\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\63d9a320484d40551e334698c8aad5a1\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-4\7ab7586f782e1dfaf77998583739c3df\transformed\glide-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-4\7ab7586f782e1dfaf77998583739c3df\transformed\glide-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ddcb1ff1872ec5c605d81bc7bf4df707\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ddcb1ff1872ec5c605d81bc7bf4df707\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\d28c72f89ae3b193aba1ffbddc0ccc99\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\d28c72f89ae3b193aba1ffbddc0ccc99\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\3eff0408d770257651ac0655ad8bf002\transformed\activity-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\3eff0408d770257651ac0655ad8bf002\transformed\activity-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\3bde3060b7ab0f6cb97bcfe13b8032cc\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\3bde3060b7ab0f6cb97bcfe13b8032cc\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1708c35514dec8ed8eb018c487cc03fa\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1708c35514dec8ed8eb018c487cc03fa\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.pedroSG94:RTSP-Server:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\4d79b6d4d4295ef1568b5bc1326c2a8b\transformed\RTSP-Server-1.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.github.pedroSG94:RTSP-Server:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\4d79b6d4d4295ef1568b5bc1326c2a8b\transformed\RTSP-Server-1.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.github.pedroSG94.RootEncoder:library:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\edc91c129b6f26810f57bfdc275dfe97\transformed\library-2.3.5\AndroidManifest.xml:5:5-44
MERGED from [com.github.pedroSG94.RootEncoder:library:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\edc91c129b6f26810f57bfdc275dfe97\transformed\library-2.3.5\AndroidManifest.xml:5:5-44
MERGED from [io.github.beyka:Android-TiffBitmapFactory:*******] C:\Users\<USER>\.gradle\caches\transforms-4\a9b07e61d85a8265fe24df7d55359a0e\transformed\Android-TiffBitmapFactory-*******\AndroidManifest.xml:5:5-7:41
MERGED from [io.github.beyka:Android-TiffBitmapFactory:*******] C:\Users\<USER>\.gradle\caches\transforms-4\a9b07e61d85a8265fe24df7d55359a0e\transformed\Android-TiffBitmapFactory-*******\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1bb32382be12bddb34483d2d06f1cb61\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1bb32382be12bddb34483d2d06f1cb61\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\14773883bdf56808997dc83a9daf4940\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\14773883bdf56808997dc83a9daf4940\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b53131b5458fe3881864d227138d78a2\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b53131b5458fe3881864d227138d78a2\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a6ffe7991f68b5934341f0e5951ddb6\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a6ffe7991f68b5934341f0e5951ddb6\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\93159e3bb7db567d42a4ec2620aa6e2b\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\93159e3bb7db567d42a4ec2620aa6e2b\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\dc51b6fd7f43d251d9c379621cbf774d\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\dc51b6fd7f43d251d9c379621cbf774d\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\88a328a3529cc848c36e857e65ee451b\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\88a328a3529cc848c36e857e65ee451b\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d377f557203330d40b2d2db0ec3a5761\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d377f557203330d40b2d2db0ec3a5761\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\745ebaa56a37d14dd80824f7313a0750\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\745ebaa56a37d14dd80824f7313a0750\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\41671412a7ea8f88fd6869bf552aea66\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\41671412a7ea8f88fd6869bf552aea66\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\7d554d83977f652d4a02b1eb4eef1adb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\7d554d83977f652d4a02b1eb4eef1adb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\9306ec361073d5fc0d3e09d69f866e6f\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\9306ec361073d5fc0d3e09d69f866e6f\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\a7e7c2966fb6aefa3b8b213275feec18\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\a7e7c2966fb6aefa3b8b213275feec18\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\0d9f5962a81b4cf5303e0a56a1893241\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\0d9f5962a81b4cf5303e0a56a1893241\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\d3b9594318906c3194d9b915895fff59\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\d3b9594318906c3194d9b915895fff59\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\e259b1e507e6128237302f34029d1c26\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\e259b1e507e6128237302f34029d1c26\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c1a1084f1edd9df35d5af90fcb0c9f0\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c1a1084f1edd9df35d5af90fcb0c9f0\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\d31cab8c5e76a7f069e99ecc998d7554\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\d31cab8c5e76a7f069e99ecc998d7554\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\3f337421a01a4d4f98be4377e3140ab8\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\3f337421a01a4d4f98be4377e3140ab8\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a03e9269f3e4a35c8e91891120a4c47\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a03e9269f3e4a35c8e91891120a4c47\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\ca0b77dfec151509a5288f6b0fefffa8\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\ca0b77dfec151509a5288f6b0fefffa8\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c9fb506d1bb02ca98280e33e28d9b38\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c9fb506d1bb02ca98280e33e28d9b38\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\46991202cb4e64dbcf66035a06968101\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\46991202cb4e64dbcf66035a06968101\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\d17a7b93296c4e904c81c859d3d35a8e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\d17a7b93296c4e904c81c859d3d35a8e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\150fb8ca893ce7c77fec935909a6b8ce\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\150fb8ca893ce7c77fec935909a6b8ce\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\233bd232f99c0a8bddbdbda7953b7420\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\233bd232f99c0a8bddbdbda7953b7420\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-4\a96ab53f07d1b8523325502943be50c7\transformed\gifdecoder-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-4\a96ab53f07d1b8523325502943be50c7\transformed\gifdecoder-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.3] C:\Users\<USER>\.gradle\caches\transforms-4\1685f1d4f4e579e8a3208f900844dce6\transformed\exifinterface-1.3.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.3] C:\Users\<USER>\.gradle\caches\transforms-4\1685f1d4f4e579e8a3208f900844dce6\transformed\exifinterface-1.3.3\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.pedroSG94.RootEncoder:encoder:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\7e88f7c1147c774d1e0810447c002a13\transformed\encoder-2.3.5\AndroidManifest.xml:5:5-44
MERGED from [com.github.pedroSG94.RootEncoder:encoder:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\7e88f7c1147c774d1e0810447c002a13\transformed\encoder-2.3.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7ae094211e49000700370b1c27573f88\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7ae094211e49000700370b1c27573f88\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\0c6ef4f1c2196c717ff61866694eb142\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\0c6ef4f1c2196c717ff61866694eb142\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\254e0728763919256b38003ddd64de89\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\254e0728763919256b38003ddd64de89\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab17ed870356635c7759a4b7d6b78f22\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ab17ed870356635c7759a4b7d6b78f22\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fb9b41813e9f5ff7e2db705f8eda4bcf\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fb9b41813e9f5ff7e2db705f8eda4bcf\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ffd6c0d30963042505ec9a438f5048eb\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ffd6c0d30963042505ec9a438f5048eb\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.pedroSG94.RootEncoder:rtmp:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\217b26216412ab3a46ca416c64b548fe\transformed\rtmp-2.3.5\AndroidManifest.xml:5:5-44
MERGED from [com.github.pedroSG94.RootEncoder:rtmp:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\217b26216412ab3a46ca416c64b548fe\transformed\rtmp-2.3.5\AndroidManifest.xml:5:5-44
MERGED from [com.github.pedroSG94.RootEncoder:rtsp:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\79255cdaee0505193517c38db20f1bef\transformed\rtsp-2.3.5\AndroidManifest.xml:5:5-44
MERGED from [com.github.pedroSG94.RootEncoder:rtsp:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\79255cdaee0505193517c38db20f1bef\transformed\rtsp-2.3.5\AndroidManifest.xml:5:5-44
MERGED from [com.github.pedroSG94.RootEncoder:srt:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\ea6ffb5dcddd5ac8e0a5a575f6ff726a\transformed\srt-2.3.5\AndroidManifest.xml:20:5-44
MERGED from [com.github.pedroSG94.RootEncoder:srt:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\ea6ffb5dcddd5ac8e0a5a575f6ff726a\transformed\srt-2.3.5\AndroidManifest.xml:20:5-44
MERGED from [com.github.pedroSG94.RootEncoder:common:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\256e04cb5415f9d9c05b816f873a16e3\transformed\common-2.3.5\AndroidManifest.xml:5:5-44
MERGED from [com.github.pedroSG94.RootEncoder:common:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\256e04cb5415f9d9c05b816f873a16e3\transformed\common-2.3.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\1a3512344cb2d58e2558806fd3b5e1f1\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\1a3512344cb2d58e2558806fd3b5e1f1\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a6ffe7991f68b5934341f0e5951ddb6\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\7d554d83977f652d4a02b1eb4eef1adb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\7d554d83977f652d4a02b1eb4eef1adb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\d17a7b93296c4e904c81c859d3d35a8e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\d17a7b93296c4e904c81c859d3d35a8e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\150fb8ca893ce7c77fec935909a6b8ce\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\150fb8ca893ce7c77fec935909a6b8ce\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a6ffe7991f68b5934341f0e5951ddb6\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a6ffe7991f68b5934341f0e5951ddb6\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a6ffe7991f68b5934341f0e5951ddb6\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a6ffe7991f68b5934341f0e5951ddb6\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a6ffe7991f68b5934341f0e5951ddb6\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a6ffe7991f68b5934341f0e5951ddb6\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a6ffe7991f68b5934341f0e5951ddb6\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\7d554d83977f652d4a02b1eb4eef1adb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\7d554d83977f652d4a02b1eb4eef1adb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\7d554d83977f652d4a02b1eb4eef1adb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c9fb506d1bb02ca98280e33e28d9b38\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c9fb506d1bb02ca98280e33e28d9b38\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c9fb506d1bb02ca98280e33e28d9b38\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
permission#com.android.rockchip.mediacodecnew.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c9fb506d1bb02ca98280e33e28d9b38\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c9fb506d1bb02ca98280e33e28d9b38\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c9fb506d1bb02ca98280e33e28d9b38\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c9fb506d1bb02ca98280e33e28d9b38\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c9fb506d1bb02ca98280e33e28d9b38\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
uses-permission#com.android.rockchip.mediacodecnew.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c9fb506d1bb02ca98280e33e28d9b38\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\3c9fb506d1bb02ca98280e33e28d9b38\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\d17a7b93296c4e904c81c859d3d35a8e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\d17a7b93296c4e904c81c859d3d35a8e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\d17a7b93296c4e904c81c859d3d35a8e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\d17a7b93296c4e904c81c859d3d35a8e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\d17a7b93296c4e904c81c859d3d35a8e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\d17a7b93296c4e904c81c859d3d35a8e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\d17a7b93296c4e904c81c859d3d35a8e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\d17a7b93296c4e904c81c859d3d35a8e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\d17a7b93296c4e904c81c859d3d35a8e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\d17a7b93296c4e904c81c859d3d35a8e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\d17a7b93296c4e904c81c859d3d35a8e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\d17a7b93296c4e904c81c859d3d35a8e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\d17a7b93296c4e904c81c859d3d35a8e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\d17a7b93296c4e904c81c859d3d35a8e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\d17a7b93296c4e904c81c859d3d35a8e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\d17a7b93296c4e904c81c859d3d35a8e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\d17a7b93296c4e904c81c859d3d35a8e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\d17a7b93296c4e904c81c859d3d35a8e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\d17a7b93296c4e904c81c859d3d35a8e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\d17a7b93296c4e904c81c859d3d35a8e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\d17a7b93296c4e904c81c859d3d35a8e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
