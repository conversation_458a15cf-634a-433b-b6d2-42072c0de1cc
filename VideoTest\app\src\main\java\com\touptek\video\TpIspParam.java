package com.touptek.video;

import android.content.Context;
import android.content.SharedPreferences;

import com.touptek.video.internal.TpSerialManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * TpIspParam 枚举类用于定义和管理摄像头ISP参数。
 * <p>
 * 此类提供了摄像头图像信号处理(ISP)参数的简单管理接口。
 * 每个枚举项对应一个ISP参数及其唯一的整数值，支持与设备通信。
 * </p>
 *
 * <p><b>核心功能：</b></p>
 * <ul>
 *   <li>ISP参数定义</li>
 *   <li>参数值的存储与读取</li>
 *   <li>参数变化监听</li>
 * </ul>
 *
 * <p><b>使用示例：</b></p>
 * <pre>{@code
 * // 初始化
 * TpIspParam.init(context);
 *
 * // 设置参数
 * TpIspParam.updateParam(TpIspParam.TOUPTEK_PARAM_BRIGHTNESS, 60);
 *
 * // 读取参数当前值
 * int brightness = TpIspParam.getCurrentValue(TpIspParam.TOUPTEK_PARAM_BRIGHTNESS);
 *
 * // 监听参数变化
 * TpIspParam.addOnDataChangedListener(new TpIspParam.OnDataChangedListener() {
 *     @Override
 *     public void onDataChanged(TpIspParam param, int newValue) {
 *         Log.d(TAG, "参数变化: " + param.name() + " = " + newValue);
 *     }
 *
 *     @Override
 *     public void onLongDataChanged(TpIspParam param, long newValue) {
 *         Log.d(TAG, "长整型参数变化: " + param.name() + " = " + newValue);
 *     }
 * });
 * }</pre>
 *
 * <AUTHOR>
 * @version 2.0
 */
public enum TpIspParam
{
    /** 版本号，只读参数 */
    TOUPTEK_PARAM_VERSION(0x0),

    /** 曝光模式选择，0:手动曝光，1:自动曝光 */
    TOUPTEK_PARAM_EXPOSURECHOICE(0x1),  

    /** 曝光补偿，调整自动曝光的目标亮度 */
    TOUPTEK_PARAM_EXPOSURECOMPENSATION(0x2),

    /** 曝光时间，单位为毫秒，仅在手动曝光模式下有效 */
    TOUPTEK_PARAM_EXPOSURETIME(0x3),

    /** 曝光增益，放大传感器信号，仅在手动曝光模式下有效 */
    TOUPTEK_PARAM_EXPOSUREGAIN(0x4),

    /** 白平衡模式选择，0:手动，1:自动，2:ROI */
    TOUPTEK_PARAM_WBCHOICE(0x5),

    /** 红色通道的白平衡增益，仅在手动白平衡模式下有效 */
    TOUPTEK_PARAM_WBREDGAIN(0x6),

    /** 绿色通道的白平衡增益，仅在手动白平衡模式下有效 */
    TOUPTEK_PARAM_WBGREENGAIN(0x7),

    /** 蓝色通道的白平衡增益，仅在手动白平衡模式下有效 */
    TOUPTEK_PARAM_WBBLUEGAIN(0x8),

    /** 锐化参数，控制图像边缘清晰度 */
    TOUPTEK_PARAM_SHARPNESS(0x9),

    /** 降噪参数，减少图像噪点 */
    TOUPTEK_PARAM_DENOISE(0xA),

    /** 镜像效果，0:关闭，1:开启 */
    TOUPTEK_PARAM_MIRROR(0xB),

    /** 翻转效果，0:关闭，1:开启 */
    TOUPTEK_PARAM_FLIP(0xC),

    /** 饱和度，控制色彩鲜艳程度 */
    TOUPTEK_PARAM_SATURATION(0xD),

    /** Gamma校正，调整图像亮度非线性分布 */
    TOUPTEK_PARAM_GAMMA(0xE),

    /** 对比度，控制图像明暗层次 */
    TOUPTEK_PARAM_CONTRAST(0xF),

    /** 频率，用于抑制电源频率引起的闪烁，0:50Hz，1:60Hz，2:禁用 */
    TOUPTEK_PARAM_HZ(0x10),

    /** 亮度，调整图像整体明亮度 */
    TOUPTEK_PARAM_BRIGHTNESS(0x11),

    /** 色调，调整图像色彩倾向 */
    TOUPTEK_PARAM_HUE(0x12),

    /** 彩色/灰度模式选择，0:彩色，1:灰度 */
    TOUPTEK_PARAM_COLORORGRAY(0x13),

    /** 带宽控制，影响图像处理的速度和质量 */
    TOUPTEK_PARAM_BANDWIDTH(0x14),

    /** 色彩色调，预设的色彩风格 */
    TOUPTEK_PARAM_COLORTONE(0x15),

    /** 彩色温度红色通道增益，用于手动调整色温 */
    TOUPTEK_PARAM_CTREDGAIN(0x16),

    /** 彩色温度绿色通道增益，用于手动调整色温 */
    TOUPTEK_PARAM_CTGREENGAIN(0x17),

    /** 彩色温度蓝色通道增益，用于手动调整色温 */
    TOUPTEK_PARAM_CTBLUEGAIN(0x18),

    /** 暗部增强，提升阴影区域细节 */
    TOUPTEK_PARAM_DARKENHANCE(0x19),

    /** 宽动态范围曝光比率，增强高对比度场景的细节 */
    TOUPTEK_PARAM_WDREXPRATIO(0x1A),

    /** 低动态范围对比度比率，调整低光照条件下的对比度 */
    TOUPTEK_PARAM_LDCRATIO(0x1B),

    /** ROI区域左边界坐标，仅在ROI白平衡模式下有效 */
    TOUPTEK_PARAM_ROI_LEFT(0xF1),

    /** ROI区域上边界坐标，仅在ROI白平衡模式下有效 */
    TOUPTEK_PARAM_ROI_TOP(0xF2),

    /** ROI区域宽度，仅在ROI白平衡模式下有效 */
    TOUPTEK_PARAM_ROI_WIDTH(0xF3),

    /** ROI区域高度，仅在ROI白平衡模式下有效 */
    TOUPTEK_PARAM_ROI_HEIGHT(0xF4),

    /** 场景选择参数，预设的参数组合 */
    TOUPTEK_PARAM_ISP_DEFAULT_TYPE(0xF5);


    /** 申请参数范围命令码，用于从设备获取参数范围信息 */
    private static final int REQUEST_PARAM_RANGES = 0xF0;


    /** 枚举值对应的整数值，用于串口通信 */
    private final int value;
    
    /** 用于存储参数值的SharedPreferences实例，确保应用重启后参数值不丢失 */
    private static SharedPreferences sharedPreferences;
    
    /** 数据变化监听器列表，支持多个监听器同时接收参数变化通知 */
    private static final List<OnDataChangedListener> dataChangedListeners = new ArrayList<>();

    
    /** 串口实例，用于与设备通信 */
    private static TpSerialManager serialInstance;

    /** 串口状态监听器，用于监听串口连接状态变化 */
    private static OnSerialStateChangedListener serialStateListener;



    // ===== 场景管理相关字段 =====

    /** 场景存储的SharedPreferences文件名 */
    private static final String SCENE_PREFS_NAME = "TouptekIspScenes";

    /** 场景存储的SharedPreferences实例 */
    private static SharedPreferences scenePreferences;

    /** 场景数据键前缀 */
    private static final String KEY_SCENE_PREFIX = "scene_data_";

    // ===== ISP场景类型定义 =====

    // ===== 场景表管理 =====

    /** 场景配置信息 */
    private static class SceneConfig {
        final int ispDefaultValue;  // ISP_DEFAULT_TYPE值，用于请求参数
        final String description;   // 场景描述
        final boolean isProtected;  // 是否为系统保护场景

        SceneConfig(int ispDefaultValue, String description, boolean isProtected) {
            this.ispDefaultValue = ispDefaultValue;
            this.description = description;
            this.isProtected = isProtected;
        }
    }

    /** 系统预设场景配置表 */
    private static final Map<String, SceneConfig> SYSTEM_SCENES = new HashMap<String, SceneConfig>() {{
        put("生物", new SceneConfig(0, "系统预设生物镜场景，适用于生物显微镜观察", true));
        put("体视", new SceneConfig(50, "系统预设体视镜场景，适用于体视显微镜观察", true));
    }};



    // ===== 统一场景管理API =====

    /**
     * 获取所有可用场景名称（系统+用户）
     * @return 所有场景名称列表
     */
    public static List<String> getAllSceneNames() {
        List<String> allScenes = new ArrayList<>();

        // 添加系统场景
        allScenes.addAll(SYSTEM_SCENES.keySet());

        // 扫描用户场景
        if (scenePreferences != null) {
            Map<String, ?> allPrefs = scenePreferences.getAll();
            for (String key : allPrefs.keySet()) {
                if (key.startsWith(KEY_SCENE_PREFIX)) {
                    String sceneName = key.substring(KEY_SCENE_PREFIX.length());
                    if (!SYSTEM_SCENES.containsKey(sceneName)) {
                        allScenes.add(sceneName);
                    }
                }
            }
        }

        return allScenes;
    }

    /**
     * 应用指定场景（统一入口）
     * <p>
     * 统一处理系统场景和用户场景。系统场景在首次使用时从设备获取并保存，
     * 后续使用时直接从本地数据应用，与用户场景使用相同的逻辑。
     * </p>
     * @param sceneName 场景名称
     * @return 成功应用的参数数量，-1表示失败
     */
    public static int applyScene(String sceneName) {
        if (sceneName == null || sceneName.trim().isEmpty()) {
            return -1;
        }
        if (scenePreferences == null) {
            return -1;
        }

        String trimmedName = sceneName.trim();

        // 先尝试从保存的数据中读取
        String sceneKey = KEY_SCENE_PREFIX + trimmedName;
        String sceneDataJson = scenePreferences.getString(sceneKey, null);



        if (sceneDataJson != null) {
            // 有保存的数据，直接使用（系统场景和用户场景统一处理）
            System.out.println("应用已保存的场景: " + trimmedName);
            return applySceneFromJson(sceneDataJson);
        }

        // 没有保存的数据，检查是否是系统场景
        if (SYSTEM_SCENES.containsKey(trimmedName)) {
            // 系统场景：按需初始化
            return initializeAndApplySystemScene(trimmedName);
        }

        // 既不是保存的场景，也不是系统场景
        System.err.println("场景不存在: " + trimmedName);
        return -1;
    }

    /**
     * 按需初始化并应用系统场景
     * @param sceneName 系统场景名称
     * @return 成功应用的参数数量，-1表示失败
     */
    private static int initializeAndApplySystemScene(String sceneName) {
        if (!SYSTEM_SCENES.containsKey(sceneName)) {
            return -1;
        }

        System.out.println("正在初始化系统场景: " + sceneName);

        try {
            SceneConfig config = SYSTEM_SCENES.get(sceneName);

            // 设置ISP_DEFAULT_TYPE参数
            updateParam(TOUPTEK_PARAM_ISP_DEFAULT_TYPE, config.ispDefaultValue);

            // 请求参数范围并等待完成
            requestAllParamRanges(true);

            // 检查参数范围是否接收完成
            if (!isRangeReceived()) {
                System.err.println("获取设备参数失败: " + sceneName);
                return -1;
            }

            // 保存并应用默认值
            int savedCount = saveAllDefaultValuesToLocal(true);
            if (savedCount > 0) {
                // 保存为场景，供下次直接使用
                int sceneParamCount = saveCurrentAsScene(sceneName);
                if (sceneParamCount > 0) {
                    System.out.println("成功初始化系统场景: " + sceneName + ", 参数数量: " + savedCount);
                } else {
                    System.err.println("保存场景失败: " + sceneName);
                }
            }

            return savedCount;
        } catch (Exception e) {
            System.err.println("初始化系统场景失败: " + sceneName + " - " + e.getMessage());
            return -1;
        }
    }

    /**
     * 从JSON数据应用场景
     * @param sceneDataJson JSON格式的场景数据
     * @return 成功应用的参数数量，-1表示失败
     */
    private static int applySceneFromJson(String sceneDataJson) {
        try {
            JSONObject sceneData = new JSONObject(sceneDataJson);
            JSONObject paramValues = sceneData.getJSONObject("paramValues");

            int appliedCount = 0;
            java.util.Iterator<String> keys = paramValues.keys();
            while (keys.hasNext()) {
                String paramName = keys.next();
                try {
                    TpIspParam param = TpIspParam.valueOf(paramName);
                    int value = paramValues.getInt(paramName);
                    updateParam(param, value);
                    appliedCount++;
                } catch (Exception ignored) {
                    // 跳过无效参数
                }
            }
            return appliedCount;
        } catch (JSONException e) {
            System.err.println("解析场景数据失败");
            return -1;
        }
    }







    // ===== 参数存储相关字段 =====

    /** 参数范围持久化存储 */
    private static final String PARAM_RANGES_PREFS_NAME = "tp_param_ranges";
    private static final String KEY_RANGES_VERSION = "ranges_version";
    private static SharedPreferences rangePreferences;

    /** 默认参数范围接收完成标志，true表示已接收完成 */
    private static Boolean allParamsRangeReceived = false;
    /**
     * 构造函数
     *
     * @param value 枚举项对应的整数值
     */
    TpIspParam(int value)
    {
        this.value = value;
    }

    // ===== 内部辅助方法 =====

    /**
     * 从指定Map中获取参数值的通用方法
     * @param param 参数
     * @param map 存储参数值的Map
     * @param defaultIfNull 参数为null时的默认值
     * @param builtinProvider 获取内置默认值的方法
     * @return 参数值
     */
    private static int getValueFromMap(TpIspParam param, Map<TpIspParam, Integer> map,
                                      int defaultIfNull, java.util.function.Function<TpIspParam, Integer> builtinProvider) {
        if (param == null) return defaultIfNull;
        if (map.containsKey(param)) {
            return map.get(param);
        }
        return builtinProvider.apply(param);
    }

    /**
     * 判断是否为ROI相关参数
     */
    private static boolean isRoiParam(TpIspParam param) {
        return param == TOUPTEK_PARAM_ROI_LEFT || param == TOUPTEK_PARAM_ROI_TOP ||
               param == TOUPTEK_PARAM_ROI_WIDTH || param == TOUPTEK_PARAM_ROI_HEIGHT;
    }

    /**
     * 判断是否为手动曝光参数
     */
    private static boolean isManualExposureParam(TpIspParam param) {
        return param == TOUPTEK_PARAM_EXPOSURETIME || param == TOUPTEK_PARAM_EXPOSUREGAIN;
    }

    /**
     * 判断是否为色温增益参数
     */
    private static boolean isColorTempParam(TpIspParam param) {
        return param == TOUPTEK_PARAM_CTREDGAIN || param == TOUPTEK_PARAM_CTGREENGAIN ||
               param == TOUPTEK_PARAM_CTBLUEGAIN;
    }

    /**
     * 根据当前模式判断是否应该跳过该参数
     */
    private static boolean shouldSkipParamForCurrentMode(TpIspParam param) {
        int exposureMode = getCurrentValue(TOUPTEK_PARAM_EXPOSURECHOICE);
        int wbChoice = getCurrentValue(TOUPTEK_PARAM_WBCHOICE);

        // 在非ROI白平衡模式下跳过ROI参数
        if (wbChoice != 2 && isRoiParam(param)) return true;

        // 在自动曝光模式下跳过手动曝光参数
        if (exposureMode == 1 && isManualExposureParam(param)) return true;

        // 在非手动白平衡模式下跳过色温增益参数
        if (wbChoice != 0 && isColorTempParam(param)) return true;

        return false;
    }

    /**
     * 获取参数的协议ID
     * <p>用于串口通信的技术细节，返回参数在通信协议中的ID值</p>
     * @return 参数在通信协议中的ID值
     */
    public int getParamId()
    {
        return value;
    }



    /**
     * 设置参数的禁用状态
     */
    public static void setParamDisabled(TpIspParam param, boolean isDisable) {
        if (rangePreferences == null) return;
        rangePreferences.edit().putBoolean(param.name() + "_disabled", isDisable).apply();
    }

    /**
     * 设置参数的最小值
     */
    public static void setParamMinValue(TpIspParam param, int minValue) {
        if (rangePreferences == null) return;
        rangePreferences.edit().putInt(param.name() + "_min", minValue).apply();
    }

    /**
     * 设置参数的最大值
     */
    public static void setParamMaxValue(TpIspParam param, int maxValue) {
        if (rangePreferences == null) return;
        rangePreferences.edit().putInt(param.name() + "_max", maxValue).apply();
    }

    /**
     * 设置参数的默认值
     */
    public static void setParamDefault(TpIspParam param, int defaultValue) {
        if (rangePreferences == null) return;
        SharedPreferences.Editor editor = rangePreferences.edit();
        editor.putInt(param.name() + "_default", defaultValue);

        // 只在版本号变化时保存（避免重复保存）
        long currentVersion = getCurrentValue(TOUPTEK_PARAM_VERSION);
        if (currentVersion != -1) {
            long savedVersion = rangePreferences.getLong(KEY_RANGES_VERSION, -1);
            if (savedVersion != currentVersion) {
                editor.putLong(KEY_RANGES_VERSION, currentVersion);
                System.out.println("保存设备版本号: " + currentVersion);
            }
        }

        editor.apply();
    }

    /**
     * 设置参数的完整范围（包含禁用状态）
     * 
     * @param param 参数类型
     * @param minValue 最小值
     * @param maxValue 最大值
     * @param defaultValue 默认值
     * @param isDisable 是否禁用
     */
    public static void setParamRange(TpIspParam param, boolean isDisable, int minValue, int maxValue, int defaultValue) {
        if (rangePreferences == null) return;

        SharedPreferences.Editor editor = rangePreferences.edit();
        editor.putInt(param.name() + "_min", minValue);
        editor.putInt(param.name() + "_max", maxValue);
        editor.putInt(param.name() + "_default", defaultValue);
        editor.putBoolean(param.name() + "_disabled", isDisable);
        editor.apply();
    }

    /**
     * 设置所有参数范围已接收标志
     * @param ParamsRangeReceived 参数范围接收状态，true表示已接收完成，false表示未接收
     */
    public static void setParamsRangeReceived(boolean ParamsRangeReceived)
    {
        allParamsRangeReceived = ParamsRangeReceived;
    }

    /**
     * 检查所有参数范围是否已接收
     * @return true表示所有参数范围已接收，false表示未接收
     */
    public static boolean isRangeReceived()
    {
        return allParamsRangeReceived;
    }


    /**
     * 参数数据结构
     * <p>
     * 包含ISP参数的完整信息，包括最小值、最大值、默认值、当前值和禁用状态。
     * 用于在UI层展示参数信息和范围，以及在业务逻辑中进行参数验证。
     * </p>
     * 
     * <p><b>字段说明：</b></p>
     * <ul>
     *   <li>min - 参数的最小允许值</li>
     *   <li>max - 参数的最大允许值</li>
     *   <li>defaultValue - 参数的默认值</li>
     *   <li>current - 参数的当前值</li>
     *   <li>isDisabled - 参数是否被禁用</li>
     * </ul>
     */
    public static class ParamData
    {
        /** 参数的最小允许值 */
        public final int min;
        
        /** 参数的最大允许值 */
        public final int max;
        
        /** 参数的默认值 */
        public final int defaultValue;
        
        /** 参数的当前值 */
        public int current;
        
        /** 参数是否被禁用，true表示禁用，false表示启用 */
        public final boolean isDisabled;

        /**
         * 构造一个新的参数数据对象
         * 
         * @param min 参数的最小允许值
         * @param max 参数的最大允许值
         * @param defaultValue 参数的默认值
         * @param current 参数的当前值
         * @param isDisabled 参数是否被禁用
         */
        public ParamData(int min, int max, int defaultValue, int current, boolean isDisabled)
        {
            this.min = min;
            this.max = max;
            this.defaultValue = defaultValue;
            this.current = current;
            this.isDisabled = isDisabled;
        }

        @Override
        public String toString()
        {
            return String.format("ParamData{min=%d, max=%d, default=%d, current=%d, disabled=%s}",
                    min, max, defaultValue, current, isDisabled);
        }
    }


    /**
     * 初始化 TpIspParam 和串口通信。
     * <p>
     * 此方法应在应用启动时调用，用于初始化参数存储和串口监控。
     * 初始化过程包括创建SharedPreferences实例、初始化串口通信、设置串口状态回调等。
     * </p>
     *
     * @param context 应用上下文，用于获取 SharedPreferences 实例
     */
    public static void init(Context context)
    {
        if (sharedPreferences == null)
        {
            sharedPreferences = context.getSharedPreferences("TouptekIspParams", Context.MODE_PRIVATE);
        }

        // 初始化场景管理的SharedPreferences
        if (scenePreferences == null)
        {
            scenePreferences = context.getSharedPreferences(SCENE_PREFS_NAME, Context.MODE_PRIVATE);
        }

        // 初始化参数范围存储
        if (rangePreferences == null)
        {
            rangePreferences = context.getSharedPreferences(PARAM_RANGES_PREFS_NAME, Context.MODE_PRIVATE);
            // 检查是否需要预置默认数据
            initializeDefaultRangesIfNeeded();
            // 加载参数范围数据
            loadParamRangesFromLocal();
        }

        if (serialInstance == null)
        {
            serialInstance = new TpSerialManager();
            serialInstance.setDeviceStateCallback(new TpSerialManager.DeviceStateCallback()
            {
                @Override
                public void onDeviceStateChanged(boolean connected)
                {
                    // 直接调用串口状态监听器
                    if (serialStateListener != null)
                    {
                        serialStateListener.onSerialStateChanged(connected);
                    }
                }
            });
            serialInstance.startMonitor();
        }
    }

    /**
     * 如果本地没有任何参数范围数据，预置默认数据
     */
    private static void initializeDefaultRangesIfNeeded() {
        if (rangePreferences == null) return;

        // 检查是否已有数据
        long savedVersion = rangePreferences.getLong(KEY_RANGES_VERSION, -1);
        if (savedVersion != -1) {
            System.out.println("本地已有参数范围数据，版本: " + savedVersion);
            return;
        }

        System.out.println("首次使用，预置默认参数范围数据...");

        SharedPreferences.Editor editor = rangePreferences.edit();

        // 预置所有参数的默认范围
        for (TpIspParam param : TpIspParam.values()) {
            DefaultRange range = getDefaultRangeForParam(param);
            editor.putInt(param.name() + "_min", range.min);
            editor.putInt(param.name() + "_max", range.max);
            editor.putInt(param.name() + "_default", range.defaultValue);
            editor.putBoolean(param.name() + "_disabled", range.disabled);
        }

        // 设置版本号为0，表示这是预置的默认数据
        editor.putLong(KEY_RANGES_VERSION, 0);

        editor.apply();

        System.out.println("默认参数范围数据预置完成");
    }

    /**
     * 获取参数的默认范围（从原ParamConfigManager迁移过来的数据）
     */
    private static DefaultRange getDefaultRangeForParam(TpIspParam param) {
        switch (param) {
            case TOUPTEK_PARAM_BRIGHTNESS:
                return new DefaultRange(0, 100, 50, false);
            case TOUPTEK_PARAM_CONTRAST:
                return new DefaultRange(0, 100, 50, false);
            case TOUPTEK_PARAM_SATURATION:
                return new DefaultRange(0, 100, 50, false);
            case TOUPTEK_PARAM_SHARPNESS:
                return new DefaultRange(0, 100, 20, false);
            case TOUPTEK_PARAM_GAMMA:
                return new DefaultRange(1, 20, 7, false);
            case TOUPTEK_PARAM_WBREDGAIN:
            case TOUPTEK_PARAM_WBGREENGAIN:
            case TOUPTEK_PARAM_WBBLUEGAIN:
                return new DefaultRange(1, 4095, 512, false);
            case TOUPTEK_PARAM_EXPOSURETIME:
                return new DefaultRange(0, 1000, 10, false);
            case TOUPTEK_PARAM_EXPOSUREGAIN:
                return new DefaultRange(0, 55, 0, false);
            case TOUPTEK_PARAM_EXPOSURECHOICE:
                return new DefaultRange(0, 1, 1, false);
            case TOUPTEK_PARAM_EXPOSURECOMPENSATION:
                return new DefaultRange(0, 25, 6, false);
            case TOUPTEK_PARAM_WBCHOICE:
                return new DefaultRange(0, 2, 0, false);
            case TOUPTEK_PARAM_DENOISE:
                return new DefaultRange(0, 100, 10, false);
            case TOUPTEK_PARAM_MIRROR:
            case TOUPTEK_PARAM_FLIP:
                return new DefaultRange(0, 1, 0, false);
            case TOUPTEK_PARAM_HZ:
                return new DefaultRange(0, 2, 2, false);
            case TOUPTEK_PARAM_HUE:
                return new DefaultRange(0, 100, 50, false);
            case TOUPTEK_PARAM_COLORORGRAY:
                return new DefaultRange(0, 1, 0, false);
            case TOUPTEK_PARAM_BANDWIDTH:
                return new DefaultRange(1024, 35840, 15360, false);
            case TOUPTEK_PARAM_COLORTONE:
                return new DefaultRange(0, 3, 0, false);
            case TOUPTEK_PARAM_CTREDGAIN:
            case TOUPTEK_PARAM_CTGREENGAIN:
            case TOUPTEK_PARAM_CTBLUEGAIN:
                return new DefaultRange(0, 128, 0, false);
            case TOUPTEK_PARAM_DARKENHANCE:
                return new DefaultRange(0, 100, 40, false);
            case TOUPTEK_PARAM_WDREXPRATIO:
                return new DefaultRange(1, 16, 10, false);
            case TOUPTEK_PARAM_LDCRATIO:
                return new DefaultRange(-30, 30, 0, false);
            case TOUPTEK_PARAM_ROI_LEFT:
                return new DefaultRange(0, 3808, 1680, false);
            case TOUPTEK_PARAM_ROI_TOP:
                return new DefaultRange(0, 2128, 945, false);
            case TOUPTEK_PARAM_ROI_WIDTH:
                return new DefaultRange(2, 3808, 480, false);
            case TOUPTEK_PARAM_ROI_HEIGHT:
                return new DefaultRange(2, 2128, 270, false);
            default:
                return new DefaultRange(0, 100, 50, false);
        }
    }

    private static class DefaultRange {
        final int min, max, defaultValue;
        final boolean disabled;

        DefaultRange(int min, int max, int defaultValue, boolean disabled) {
            this.min = min;
            this.max = max;
            this.defaultValue = defaultValue;
            this.disabled = disabled;
        }
    }

    /**
     * 从本地加载参数范围数据
     */
    private static void loadParamRangesFromLocal() {
        if (rangePreferences == null) return;

        long savedVersion = rangePreferences.getLong(KEY_RANGES_VERSION, -1);
        if (savedVersion == -1) {
            System.out.println("本地无参数范围数据");
            return;
        }

        // 检查是否有参数范围数据
        boolean hasRangeData = false;
        for (TpIspParam param : TpIspParam.values()) {
            if (rangePreferences.contains(param.name() + "_min")) {
                hasRangeData = true;
                break;
            }
        }

        if (hasRangeData) {
            setParamsRangeReceived(true);
            System.out.println("从本地加载参数范围，版本: " + savedVersion);
        } else {
            System.out.println("本地版本号存在但参数范围数据不完整");
        }
    }

    /**
     * 获取参数的最小值。
     * <p>
     * 根据不同参数类型返回其有效范围的最小值。
     * 优先使用串口接收到的值，如果没有则使用系统默认值。
     * </p>
     *
     * @param param 需要获取最小值的参数
     * @return 参数的最小值
     */
    public static int getMinValue(TpIspParam param)
    {
        if (rangePreferences == null) return 0;
        return rangePreferences.getInt(param.name() + "_min", 0);
    }

    /**
     * 获取参数的最大值。
     * <p>
     * 根据不同参数类型返回其有效范围的最大值。
     * 优先使用串口接收到的值，如果没有则使用系统默认值。
     * </p>
     *
     * @param param 需要获取最大值的参数
     * @return 参数的最大值
     */
    public static int getMaxValue(TpIspParam param)
    {
        if (rangePreferences == null) return 100;
        return rangePreferences.getInt(param.name() + "_max", 100);
    }

    /**
     * 获取参数的默认值
     * <p>
     * 根据不同参数类型返回其默认值。
     * 优先使用串口接收到的值，如果没有则使用系统默认值。
     * </p>
     *
     * @param param 需要获取默认值的参数
     * @return 参数的默认值
     */
    public static int getDefaultValue(TpIspParam param)
    {
        if (rangePreferences == null) return 50;
        return rangePreferences.getInt(param.name() + "_default", 50);
    }



    /**
     * 获取参数的禁用状态
     * <p>
     * 根据不同参数类型返回其使能状态。
     * 优先使用串口接收到的值，如果没有则使用系统默认值。
     * </p>
     * @param param 参数类型
     * @return true表示禁用，false表示启用，默认为false（启用）
     */
    public static boolean getIsDisableValue(TpIspParam param) {
        if (param == null) {
            return false;
        }
        if (rangePreferences == null) return false;
        return rangePreferences.getBoolean(param.name() + "_disabled", false);
    }


    /**
     * 获取参数的完整信息
     * <p>返回指定参数的完整信息，包括范围、默认值、当前值等</p>
     * @param param 需要获取信息的参数
     * @return 包含参数完整信息的 {@link ParamData} 对象
     */
    public static ParamData getParamInfo(TpIspParam param)
    {
        int min = getMinValue(param);
        int max = getMaxValue(param);
        int defaultValue = getDefaultValue(param);
        int current = getCurrentValue(param);
        boolean isDisabled = getIsDisableValue(param);

        // 如果没有存储值，使用默认值
        if (current == -1)
        {
            current = defaultValue;
            // 保存默认值到本地
            saveToLocal(param, defaultValue);
        }

        return new ParamData(min, max, defaultValue, current, isDisabled);
    }


    /**
     * 获取所有参数的完整信息
     * <p>返回所有ISP参数的完整信息列表，用于UI显示或批量管理</p>
     * @return 包含所有参数完整信息的列表
     */
    public static List<ParamData> getAllParamInfo()
    {
        List<ParamData> paramDataList = new ArrayList<>();
        for (TpIspParam param : TpIspParam.values())
        {
            paramDataList.add(getParamInfo(param));
        }
        return paramDataList;
    }


    /**
     * 同步所有当前值到设备
     * <p>将所有参数的当前值同步到设备。根据当前的模式设置，会自动跳过某些不适用的参数：</p>
     * <ul>
     *   <li>在自动曝光模式下跳过手动曝光参数</li>
     *   <li>在非ROI白平衡模式下跳过ROI参数</li>
     *   <li>在非手动白平衡模式下跳过色温增益参数</li>
     *   <li>始终跳过只读参数（如版本号）</li>
     * </ul>
     */
    public static void syncAllCurrentValuesToDevice() {
        android.util.Log.i("TpIspParam", "开始同步所有当前值到设备...");

        int sentCount = 0;
        for (TpIspParam param : TpIspParam.values()) {
            try {
                // 1. 始终跳过版本号参数，它是只读的
                if (param == TOUPTEK_PARAM_VERSION) {
                    continue;
                }

                // 2. 根据当前模式判断是否跳过参数
                if (shouldSkipParamForCurrentMode(param)) {
                    continue;
                }
                
                // 获取参数的当前值
                int currentValue = getCurrentValue(param);

                // 只发送已设置过的参数（值不为-1）
                if (currentValue != -1) {
                    sendToDevice(param, currentValue);
                    sentCount++;
                    android.util.Log.d("TpIspParam", "同步参数: " + param.name() + " = " + currentValue);

                    // 添加小延时避免过于频繁的发送，确保设备有足够时间处理命令
                    try {
                        Thread.sleep(10);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                }
            } catch (Exception e) {
                android.util.Log.e("TpIspParam", "发送参数 " + param.name() + " 失败: " + e.getMessage());
            }
        }
        android.util.Log.i("TpIspParam", "参数同步完成，共同步 " + sentCount + " 个参数");
    }

    /**
     * 根据整数值获取枚举项。
     * <p>
     * 将设备返回的命令值转换为对应的枚举项。
     * </p>
     *
     * @param i 整数值，对应枚举项的value
     * @return 匹配的枚举项，如果没有匹配项则返回null
     */
    public static TpIspParam fromInt(int i)
    {
        for (TpIspParam param : TpIspParam.values())
        {
            if (param.getParamId() == i)
            {
                return param;
            }
        }
        return null; // 没有匹配项
    }



    /**
     * 检查串口是否已连接
     * <p>
     * 用于判断当前串口的连接状态。
     * </p>
     *
     * @return 串口连接状态，true 表示已连接，false 表示未连接
     */
    public static boolean isSerialConnected() 
    {
        return serialInstance != null && serialInstance.isSerialConnected();
    }

    /**
     * 设置串口状态变化监听器。
     * <p>
     * 当串口连接状态变化时，会触发此监听器的回调方法。
     * </p>
     *
     * @param listener 串口状态变化监听器实例。
     */
    public static void setOnSerialStateChangedListener(OnSerialStateChangedListener listener) 
    {
        serialStateListener = listener;
    }

    /**
     * 停止串口监控和相关资源。
     * <p>
     * 在应用退出或不再需要串口通信时调用。
     * </p>
     */
    public static void release()
    {
        // 清理串口资源
        if (serialInstance != null)
        {
            serialInstance.stopMonitor();
            serialInstance.close();
            serialInstance = null;
        }

        // 清理监听器
        dataChangedListeners.clear();
        serialStateListener = null;

        // 重置标志位
        allParamsRangeReceived = false;

        // 清理 SharedPreferences 引用
        sharedPreferences = null;
        rangePreferences = null;
    }

    /**
     * 添加数据变化监听器
     * <p>
     * 支持多个监听器同时监听参数变化。
     * </p>
     *
     * @param listener 数据变化监听器实例
     */
    public static synchronized void addOnDataChangedListener(OnDataChangedListener listener) {
        if (listener != null && !dataChangedListeners.contains(listener)) {
            dataChangedListeners.add(listener);
        }
    }

    /**
     * 移除数据变化监听器
     * <p>
     * 从监听器列表中移除指定的监听器。
     * </p>
     *
     * @param listener 要移除的监听器实例
     */
    public static synchronized void removeOnDataChangedListener(OnDataChangedListener listener) 
    {
        dataChangedListeners.remove(listener);
    }

    /**
     * 通知所有监听器数据变化
     * <p>
     * 当参数值发生变化时，通知所有已注册的监听器。
     * </p>
     *
     * @param param 发生变化的参数
     * @param value 新的参数值
     */
    private static synchronized void notifyDataChanged(TpIspParam param, int value)
    {
        // 通知新的监听器列表
        for (OnDataChangedListener listener : dataChangedListeners) 
        {
            if (listener != null) 
            {
                try {
                    listener.onDataChanged(param, value);
                } catch (Exception e) {
                    // 防止某个监听器出错影响其他监听器
                    System.err.println("Error in data change listener: " + e.getMessage());
                }
            }
        }
    }

    /**
     * 通知所有监听器长整型数据变化
     * <p>
     * 当长整型参数值发生变化时，通知所有已注册的监听器。
     * </p>
     *
     * @param param 发生变化的参数
     * @param value 新的参数值
     */
    private static synchronized void notifyLongDataChanged(TpIspParam param, long value)
    {
        // 通知新的监听器列表
        for (OnDataChangedListener listener : dataChangedListeners) {
            if (listener != null) {
                try {
                    listener.onLongDataChanged(param, value);
                } catch (Exception e) {
                    System.err.println("Error in long data change listener: " + e.getMessage());
                }
            }
        }
    }

    /**
     * 将所有参数恢复为默认值
     * <p>
     * 此方法会等待参数范围请求完成，然后再保存默认值
     * </p>
     *
     * @param sendToDevice 是否将默认值同时发送到设备
     * @return 成功保存的参数数量，-1表示等待超时
     */
    public static int saveAllDefaultValuesToLocal(boolean sendToDevice) {
        // 简单检查，不等待
        if (!isRangeReceived()) {
            System.out.println("参数范围未接收，使用默认配置");
        }

        System.out.println("开始保存所有参数默认值...");

        int savedCount = 0;
        for (TpIspParam param : TpIspParam.values()) {
            try {
                int defaultValue = getDefaultValue(param);
                if (defaultValue != 555555) {
                    boolean skipSendToDevice = shouldSkipParamForCurrentMode(param);

                    if (skipSendToDevice) {
                        saveToLocal(param, defaultValue);
                    } else if (sendToDevice) {
                        updateParam(param, defaultValue);
                    } else {
                        saveToLocal(param, defaultValue);
                    }
                    savedCount++;
                }
            } catch (Exception e) {
                System.err.println("保存参数失败: " + param.name());
            }
        }

        System.out.println("默认值保存完成，共保存 " + savedCount + " 个参数");
        return savedCount;
    }

    /**
     * 更新本地参数值，并发送到设备
     * <p>
     * 此方法执行三个操作：保存参数到本地存储、发送参数到设备、通知所有监听器参数变化。
     * 是设置ISP参数的主要方法。
     * </p>
     *
     * @param param 枚举类型 {@link TpIspParam}，表示需要更新的参数
     * @param value 要更新的参数值
     */
    public static void updateParam(TpIspParam param, int value)
    {
        // 保存到本地
        saveToLocal(param, value);
        
        // 发送到设备
        sendToDevice(param, value);

        // 添加此行来通知监听器
        notifyDataChanged(param, value);

        System.out.println("update ISP Param: " + param.name() + ": " + value);
    }
    
    /**
     * 更新本地参数值，并发送到设备，带控制字节
     * <p>
     * 此方法将执行三个操作：保存到本地、发送到设备
     * 可以指定是读取操作还是写入操作。
     * </p>
     *
     * @param param 枚举类型 {@link TpIspParam}，表示需要更新的参数
     * @param value 要更新的 int 类型数据
     * @param ctrl  控制字节，0x01表示写操作，0x00表示读操作
     */
    private static void updateParam(TpIspParam param, int value, int ctrl)
    {
        // 保存到本地
        saveToLocal(param, value);

        // 发送到设备
        sendToDevice(param, value, ctrl);

        System.out.println("update ISP Param: " + param.name() + ": " + value + " ctrl: " + (ctrl == 0x01 ? "write" : "read"));
    }
    

    /**
     * 仅保存参数到本地，不发送到设备
     *
     * @param param 枚举类型 {@link TpIspParam}，表示需要保存的参数
     * @param value 要保存的 int 类型数据
     */
    private static void saveToLocal(TpIspParam param, int value)
    {
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putInt(param.name(), value);
        editor.apply();
    }

    /**
     * 仅保存长整型参数到本地，不发送到设备
     *
     * @param param 枚举类型 {@link TpIspParam}，表示需要保存的参数
     * @param value 要保存的 long 类型数据
     */
    private static void saveToLocal(TpIspParam param, long value)
    {
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putLong(param.name(), value);
        editor.apply();
    }
    
    /**
     * 仅发送参数到设备，不保存到本地
     * <p>
     * 通过串口发送命令到设备，默认使用写入或读取控制字节。
     * </p>
     *
     * @param param 枚举类型 {@link TpIspParam}，表示需要发送的参数
     * @param value 要发送的数据值
     */
    private static void sendToDevice(TpIspParam param, int value)
    {
        int ctrl = (param == TOUPTEK_PARAM_VERSION) ? 0x00 : 0x01;
        sendToDevice(param, value, ctrl);
    }

    /**
     * 从相机设备中获取所有默认参数
     * <p>
     * 通过串口获取设备的所有默认参数范围数据。
     * </p>
     *
     */
    public static void requestAllParamRanges()
    {
        requestAllParamRanges(false);
    }

    /**
     * 从相机设备中获取所有默认参数
     * <p>
     * 通过串口获取设备的所有默认参数范围数据。
     * </p>
     *
     * @param waitForCompletion 是否等待参数接收完成，true表示阻塞等待，false表示异步执行
     */
    public static void requestAllParamRanges(boolean waitForCompletion)
    {
        // 检查串口连接状态
        if (!isSerialConnected()) {
            System.out.println("串口未连接，无法获取设备数据");
            return;
        }

        // KISS版本检查：先获取当前版本号
        long currentVersion = getCurrentValue(TOUPTEK_PARAM_VERSION);
        System.out.println("当前设备版本号: " + currentVersion);

        if (currentVersion == -1) {
            // 版本号未知，先获取版本号
            System.out.println("版本号未知，尝试获取...");
            TpSerialManager.sendCommandToSerial(0x00, TOUPTEK_PARAM_VERSION.getParamId(), 0);
            // 等待一下版本号返回
            try { Thread.sleep(200); } catch (InterruptedException ignored) {}
            currentVersion = getCurrentValue(TOUPTEK_PARAM_VERSION);
            System.out.println("重新获取的版本号: " + currentVersion);
        }

        // 简单比较版本号
        if (rangePreferences != null && currentVersion != -1) {
            long savedVersion = rangePreferences.getLong(KEY_RANGES_VERSION, -1);
            boolean rangeReceived = isRangeReceived();
            System.out.println("本地保存版本号: " + savedVersion);
            System.out.println("参数范围已接收: " + rangeReceived);

            // 如果本地版本号为0，说明是预置的默认数据，需要更新
            if (savedVersion == currentVersion && savedVersion != 0 && rangeReceived) {
                System.out.println("版本匹配（" + currentVersion + "），使用本地参数范围");
                return; // 直接返回，不执行后续获取逻辑
            } else {
                if (savedVersion == 0) {
                    System.out.println("检测到预置默认数据，更新为设备实际参数范围");
                } else {
                    System.out.println("版本不匹配或参数范围未接收，需要重新获取");
                }
            }
        } else {
            System.out.println("rangePreferences为null或版本号无效，需要重新获取");
        }

        // 版本不匹配或无本地数据，继续原有逻辑
        System.out.println("开始获取所有默认参数数据...");

        // 重置接收状态标志
        setParamsRangeReceived(false);

        // 发送获取所有默认数据的命令
        TpSerialManager.sendCommandToSerial(0x00, REQUEST_PARAM_RANGES, 0);

        // 根据参数决定是否等待数据接收完成
        if (waitForCompletion) {
            // 等待数据接收完成
            int waitCount = 0;
            final int maxWaitTime = 20; // 最大等待2秒 (20 * 100ms)

            while (!isRangeReceived() && waitCount < maxWaitTime) {
                try {
                    Thread.sleep(100); // 每100ms检查一次
                    waitCount++;

                    // 每秒打印一次进度（可选）
                    if (waitCount % 10 == 0) {
                        System.out.println("等待参数数据接收中... " + (waitCount / 10) + "秒");
                    }

                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    System.out.println("等待过程被中断");
                    break;
                }
            }

            // 检查接收结果
            if (isRangeReceived()) {
                System.out.println("参数数据接收完成，用时: " + (waitCount * 100) + "ms");
            } else {
                System.out.println("参数数据接收超时，可能设备响应较慢或连接异常");
            }
        } else {
            System.out.println("参数请求已发送，异步等待设备响应...");
        }
    }

    /**
     * 仅发送参数到设备，不保存到本地，带控制字节
     * <p>
     * 通过串口发送命令到设备，可以指定控制字节。
     * 0x00表示读取操作，0x01表示写入操作。
     * </p>
     *
     * @param param 枚举类型 {@link TpIspParam}，表示需要发送的参数
     * @param value 要发送的数据值
     * @param ctrl  控制字节，0x01表示写操作，0x00表示读操作
     */
    private static void sendToDevice(TpIspParam param, int value, int ctrl)
    {
        TpSerialManager.sendCommandToSerial(ctrl, param.getParamId(), value);
    }

    /**
     * 接收到设备数据后处理更新
     * <p>
     * 此方法用于从串口接收到数据后，更新本地参数值并触发回调。
     * 由{@link TpSerialManager#onSerialDataReceived(int[])}调用。
     * </p>
     *
     * @param param 接收到的参数类型
     * @param value 接收到的参数值
     * @param isLongValue 是否为长整型值
     */
    public static void handleReceivedData(TpIspParam param, long value, boolean isLongValue)
    {
        if (isLongValue)
        {
            saveToLocal(param, value);
            notifyLongDataChanged(param, value);
        }
        else
        {
            saveToLocal(param, (int)value);
            notifyDataChanged(param, (int)value);
        }
    }

    /**
     * 获取参数的当前值
     * <p>这是最常用的方法，用于获取参数在系统中的当前设置值</p>
     * <p>支持自动类型转换，兼容Integer和Long类型数据</p>
     * @param param 参数枚举
     * @return 参数的当前值，如果未设置则返回-1
     */
    public static int getCurrentValue(TpIspParam param)
    {
        try {
            // 首先尝试读取Integer类型
            return sharedPreferences.getInt(param.name(), -1);
        } catch (ClassCastException e) {
            try {
                // 如果失败，尝试读取Long类型并转换为Integer
                long longValue = sharedPreferences.getLong(param.name(), -1);
                if (longValue == -1) {
                    return -1;
                }
                // 检查Long值是否在Integer范围内
                if (longValue > Integer.MAX_VALUE || longValue < Integer.MIN_VALUE) {
                    System.err.println("参数值超出Integer范围: " + param.name() + " = " + longValue);
                    return -1;
                }
                return (int) longValue;
            } catch (ClassCastException e2) {
                System.err.println("无法读取参数值: " + param.name() + ", 类型不匹配");
                return -1;
            }
        }
    }



    /**
     * 获取所有参数的当前值
     * <p>返回所有参数的当前设置值，用于批量获取或数据导出</p>
     * @return 包含所有参数当前值的Map，键为参数名称，值为当前设置值
     */
    public static Map<String, ?> getAllCurrentValues()
    {
        return sharedPreferences.getAll();
    }

    /**
     * 根据索引获取对应的枚举项。
     * <p>
     * 用于在UI中通过索引选择参数。
     * </p>
     *
     * @param index 索引值，从 0 开始。
     * @return 对应的 {@link TpIspParam} 枚举项，如果索引无效则返回 null。
     */
    public static TpIspParam getParamByIndex(int index)
    {
        TpIspParam[] params = TpIspParam.values();
        if (index >= 0 && index < params.length) 
        {
            return params[index];
        }
        return null;  // 索引无效
    }

    /**
     * 数据变化监听器接口
     * <p>
     * 用于监听ISP参数值变化事件。应用可以实现此接口来接收参数变化通知，
     * 从而实时更新UI或执行其他操作。
     * </p>
     * 
     * <p><b>使用示例：</b></p>
     * <pre>{@code
     * TpIspParam.addOnDataChangedListener(new TpIspParam.OnDataChangedListener() {
     *     @Override
     *     public void onDataChanged(TpIspParam param, int newValue) {
     *         // 处理参数变化
     *         updateUI(param, newValue);
     *     }
     *     
     *     @Override
     *     public void onLongDataChanged(TpIspParam param, long newValue) {
     *         // 处理长整型参数变化（如版本号）
     *         if (param == TpIspParam.TOUPTEK_PARAM_VERSION) {
     *             showVersionInfo(newValue);
     *         }
     *     }
     * });
     * }</pre>
     */
    public interface OnDataChangedListener 
    {
        /**
         * 当 int 类型数据发生变化时调用。
         *
         * @param param    发生变化的参数 {@link TpIspParam}
         * @param newValue 新的 int 类型值
         */
        void onDataChanged(TpIspParam param, int newValue);

        /**
         * 当 long 类型数据发生变化时调用。
         *
         * @param param    发生变化的参数 {@link TpIspParam}
         * @param newValue 新的 long 类型值
         */
        void onLongDataChanged(TpIspParam param, long newValue);
    }

    /**
     * 串口状态变化监听器接口
     * <p>
     * 用于监听串口连接状态变化事件。应用可以实现此接口来接收串口连接/断开通知，
     * 从而执行相应的UI更新或业务逻辑。
     * </p>
     * 
     * <p><b>使用示例：</b></p>
     * <pre>{@code
     * TpIspParam.setOnSerialStateChangedListener(new TpIspParam.OnSerialStateChangedListener() {
     *     @Override
     *     public void onSerialStateChanged(boolean connected) {
     *         if (connected) {
     *             showConnectedStatus();
     *             // 连接后可以请求设备参数
     *             TpIspParam.requestAllParamRanges();
     *         } else {
     *             showDisconnectedStatus();
     *         }
     *     }
     * });
     * }</pre>
     */
    public interface OnSerialStateChangedListener 
    {
        /**
         * 当串口状态发生变化时调用。
         *
         * @param connected 串口是否已连接，true表示已连接，false表示已断开
         */
        void onSerialStateChanged(boolean connected);
    }







    /**
     * 参数范围数据结构（内部类）
     */
    private static class ParamRange {
        final int min;
        final int max;
        final int defaultValue;

        ParamRange(int min, int max, int defaultValue) {
            this.min = min;
            this.max = max;
            this.defaultValue = defaultValue;
        }
    }

    // ===== ISP场景管理功能 =====

    /**
     * 保存当前所有ISP参数为命名场景
     * @param sceneName 场景名称，不能为空
     * @return 成功保存的参数数量，-1表示失败
     */
    public static int saveCurrentAsScene(String sceneName) {
        if (sceneName == null || sceneName.trim().isEmpty()) {
            return -1;
        }
        if (scenePreferences == null) {
            return -1;
        }

        String trimmedName = sceneName.trim();

        // 系统场景保护逻辑
        if (SYSTEM_SCENES.containsKey(trimmedName)) {
            // 检查SharedPreferences中是否已经有这个系统场景
            String existingData = scenePreferences.getString(KEY_SCENE_PREFIX + trimmedName, null);
            if (existingData != null) {
                // 已经保存过了，不允许覆盖
                System.err.println("系统场景已存在，不能覆盖: " + trimmedName);
                return -1;
            }
            // 如果SharedPreferences中没有，允许首次保存
            System.out.println("首次保存系统场景: " + trimmedName);
        }

        try {
            JSONObject sceneData = new JSONObject();
            JSONObject paramValues = new JSONObject();

            int savedCount = 0;

            // 遍历所有ISP参数，读取当前值
            for (TpIspParam param : TpIspParam.values()) {
                // 跳过特殊参数
                if (param == TOUPTEK_PARAM_VERSION || param == TOUPTEK_PARAM_ISP_DEFAULT_TYPE) {
                    continue;
                }

                int currentValue = getCurrentValue(param);
                if (currentValue != -1) {
                    paramValues.put(param.name(), currentValue);
                    savedCount++;
                }
            }

            sceneData.put("paramValues", paramValues);
            sceneData.put("timestamp", System.currentTimeMillis());

            scenePreferences.edit()
                .putString(KEY_SCENE_PREFIX + trimmedName, sceneData.toString())
                .apply();

            return savedCount;
        } catch (JSONException e) {
            return -1;
        }
    }





    /**
     * 删除指定名称的场景
     * @param sceneName 要删除的场景名称，不能为空
     * @return true表示删除成功，false表示删除失败或场景不存在
     */
    public static boolean deleteScene(String sceneName) {
        if (sceneName == null || sceneName.trim().isEmpty()) {
            return false;
        }

        String trimmedName = sceneName.trim();

        // 系统场景不能删除
        if (SYSTEM_SCENES.containsKey(trimmedName)) {
            return false;
        }

        if (scenePreferences == null) {
            return false;
        }

        // 直接删除，不需要复杂检查
        SharedPreferences.Editor editor = scenePreferences.edit();
        editor.remove(KEY_SCENE_PREFIX + trimmedName);
        return editor.commit();
    }





    // ===== 系统场景管理方法 ====


    /**
     * 获取场景详细信息
     * @param sceneName 场景名称
     * @return 场景信息，如果场景不存在则返回null
     */
    public static SceneInfo getSceneInfo(String sceneName) {
        if (sceneName == null || sceneName.trim().isEmpty() || scenePreferences == null) {
            return null;
        }

        String trimmedName = sceneName.trim();

        // 系统场景
        if (SYSTEM_SCENES.containsKey(trimmedName)) {
            SceneConfig config = SYSTEM_SCENES.get(trimmedName);
            return new SceneInfo(trimmedName, "SYSTEM", config.description, 0, true, config.ispDefaultValue);
        }

        // 用户场景
        String sceneDataJson = scenePreferences.getString(KEY_SCENE_PREFIX + trimmedName, null);
        if (sceneDataJson == null) {
            return null;
        }

        try {
            JSONObject sceneData = new JSONObject(sceneDataJson);
            return new SceneInfo(
                trimmedName,
                "USER",
                "",
                sceneData.optLong("timestamp", 0),
                false,
                -1
            );
        } catch (JSONException e) {
            return null;
        }
    }

    /**
     * 场景信息数据类
     */
    public static class SceneInfo {
        public final String sceneName;
        public final String sceneType;
        public final String description;
        public final long timestamp;
        public final boolean isProtected;
        public final int sceneValue;

        public SceneInfo(String sceneName, String sceneType, String description,
                        long timestamp, boolean isProtected, int sceneValue) {
            this.sceneName = sceneName;
            this.sceneType = sceneType;
            this.description = description;
            this.timestamp = timestamp;
            this.isProtected = isProtected;
            this.sceneValue = sceneValue;
        }

        @Override
        public String toString() {
            return "SceneInfo{" +
                    "sceneName='" + sceneName + '\'' +
                    ", sceneType='" + sceneType + '\'' +
                    ", description='" + description + '\'' +
                    ", timestamp=" + timestamp +
                    ", isProtected=" + isProtected +
                    ", sceneValue=" + sceneValue +
                    '}';
        }
    }

    // ===== 自动系统场景初始化方法 =====











}
