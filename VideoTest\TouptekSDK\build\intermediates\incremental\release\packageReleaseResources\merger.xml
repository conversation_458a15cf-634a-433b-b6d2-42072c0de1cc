<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\hhx\rk3588\AndroidStudio\VideoTest\TouptekSDK\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\hhx\rk3588\AndroidStudio\VideoTest\TouptekSDK\src\main\res"><file name="tp_video_button_text_color" path="C:\hhx\rk3588\AndroidStudio\VideoTest\TouptekSDK\src\main\res\color\tp_video_button_text_color.xml" qualifiers="" type="color"/><file name="ic_fast_forward_white_24" path="C:\hhx\rk3588\AndroidStudio\VideoTest\TouptekSDK\src\main\res\drawable\ic_fast_forward_white_24.xml" qualifiers="" type="drawable"/><file name="ic_fast_rewind_white_24" path="C:\hhx\rk3588\AndroidStudio\VideoTest\TouptekSDK\src\main\res\drawable\ic_fast_rewind_white_24.xml" qualifiers="" type="drawable"/><file name="ic_pause_white_24" path="C:\hhx\rk3588\AndroidStudio\VideoTest\TouptekSDK\src\main\res\drawable\ic_pause_white_24.xml" qualifiers="" type="drawable"/><file name="ic_play_arrow_white_24" path="C:\hhx\rk3588\AndroidStudio\VideoTest\TouptekSDK\src\main\res\drawable\ic_play_arrow_white_24.xml" qualifiers="" type="drawable"/><file name="ic_settings_white_24" path="C:\hhx\rk3588\AndroidStudio\VideoTest\TouptekSDK\src\main\res\drawable\ic_settings_white_24.xml" qualifiers="" type="drawable"/><file name="ic_skip_next_white_24" path="C:\hhx\rk3588\AndroidStudio\VideoTest\TouptekSDK\src\main\res\drawable\ic_skip_next_white_24.xml" qualifiers="" type="drawable"/><file name="ic_skip_previous_white_24" path="C:\hhx\rk3588\AndroidStudio\VideoTest\TouptekSDK\src\main\res\drawable\ic_skip_previous_white_24.xml" qualifiers="" type="drawable"/><file name="ic_step_frame_white_24" path="C:\hhx\rk3588\AndroidStudio\VideoTest\TouptekSDK\src\main\res\drawable\ic_step_frame_white_24.xml" qualifiers="" type="drawable"/><file name="tp_speed_dropdown_background" path="C:\hhx\rk3588\AndroidStudio\VideoTest\TouptekSDK\src\main\res\drawable\tp_speed_dropdown_background.xml" qualifiers="" type="drawable"/><file name="tp_speed_item_background" path="C:\hhx\rk3588\AndroidStudio\VideoTest\TouptekSDK\src\main\res\drawable\tp_speed_item_background.xml" qualifiers="" type="drawable"/><file name="tp_speed_item_selected_background" path="C:\hhx\rk3588\AndroidStudio\VideoTest\TouptekSDK\src\main\res\drawable\tp_speed_item_selected_background.xml" qualifiers="" type="drawable"/><file name="tp_video_button_background" path="C:\hhx\rk3588\AndroidStudio\VideoTest\TouptekSDK\src\main\res\drawable\tp_video_button_background.xml" qualifiers="" type="drawable"/><file name="tp_video_controls_background" path="C:\hhx\rk3588\AndroidStudio\VideoTest\TouptekSDK\src\main\res\drawable\tp_video_controls_background.xml" qualifiers="" type="drawable"/><file name="tp_video_play_button_background" path="C:\hhx\rk3588\AndroidStudio\VideoTest\TouptekSDK\src\main\res\drawable\tp_video_play_button_background.xml" qualifiers="" type="drawable"/><file name="tp_video_progress_drawable" path="C:\hhx\rk3588\AndroidStudio\VideoTest\TouptekSDK\src\main\res\drawable\tp_video_progress_drawable.xml" qualifiers="" type="drawable"/><file name="tp_video_progress_thumb" path="C:\hhx\rk3588\AndroidStudio\VideoTest\TouptekSDK\src\main\res\drawable\tp_video_progress_thumb.xml" qualifiers="" type="drawable"/><file name="tp_video_settings_button_background" path="C:\hhx\rk3588\AndroidStudio\VideoTest\TouptekSDK\src\main\res\drawable\tp_video_settings_button_background.xml" qualifiers="" type="drawable"/><file name="tp_speed_dropdown_menu" path="C:\hhx\rk3588\AndroidStudio\VideoTest\TouptekSDK\src\main\res\layout\tp_speed_dropdown_menu.xml" qualifiers="" type="layout"/><file name="tp_video_player_controls" path="C:\hhx\rk3588\AndroidStudio\VideoTest\TouptekSDK\src\main\res\layout\tp_video_player_controls.xml" qualifiers="" type="layout"/><file path="C:\hhx\rk3588\AndroidStudio\VideoTest\TouptekSDK\src\main\res\values\attrs.xml" qualifiers=""><declare-styleable name="TpVideoPlayerView">
        <attr format="boolean" name="autoPlay"/>
    </declare-styleable></file><file path="C:\hhx\rk3588\AndroidStudio\VideoTest\TouptekSDK\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="C:\hhx\rk3588\AndroidStudio\VideoTest\TouptekSDK\src\main\res\values\tp_video_styles.xml" qualifiers=""><style name="TpVideoControlButton" parent="Widget.AppCompat.ImageButton">
        <item name="android:layout_width">56dp</item>
        <item name="android:layout_height">56dp</item>
        <item name="android:background">@drawable/tp_video_button_background</item>
        <item name="android:elevation">4dp</item>
        <item name="android:stateListAnimator">@null</item>
        <item name="android:layout_marginEnd">12dp</item>
        <item name="android:scaleType">center</item>
        <item name="android:contentDescription">控制按钮</item>
    </style><style name="TpVideoPlayButton" parent="TpVideoControlButton">
        <item name="android:layout_width">64dp</item>
        <item name="android:layout_height">64dp</item>
        <item name="android:background">@drawable/tp_video_play_button_background</item>
        <item name="android:elevation">6dp</item>
        <item name="android:contentDescription">播放/暂停</item>
    </style><style name="TpVideoSettingsButton" parent="TpVideoControlButton">
        <item name="android:background">@drawable/tp_video_settings_button_background</item>
        <item name="android:layout_marginEnd">0dp</item>
        <item name="android:contentDescription">设置</item>
    </style><style name="TpVideoTimeDisplay" parent="Widget.AppCompat.TextView">
        <item name="android:textColor">#FFFFFFFF</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:shadowColor">#80000000</item>
        <item name="android:shadowDx">1</item>
        <item name="android:shadowDy">1</item>
        <item name="android:shadowRadius">2</item>
    </style><style name="TpVideoProgressBar" parent="Widget.AppCompat.SeekBar">
        <item name="android:progressDrawable">@drawable/tp_video_progress_drawable</item>
        <item name="android:thumb">@drawable/tp_video_progress_thumb</item>
        <item name="android:paddingTop">24dp</item>
        <item name="android:paddingBottom">24dp</item>
        <item name="android:paddingLeft">16dp</item>
        <item name="android:paddingRight">16dp</item>
        <item name="android:thumbOffset">0dp</item>
    </style><style name="TpVideoSpeedDialog" parent="Theme.AppCompat.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.6</item>
        <item name="android:windowAnimationStyle">@style/TpVideoSpeedDialogAnimation</item>
    </style><style name="TpVideoSpeedDialogAnimation">
        <item name="android:windowEnterAnimation">@android:anim/fade_in</item>
        <item name="android:windowExitAnimation">@android:anim/fade_out</item>
    </style><style name="TpVideoSpeedDropdownAnimation">
        <item name="android:windowEnterAnimation">@android:anim/fade_in</item>
        <item name="android:windowExitAnimation">@android:anim/fade_out</item>
    </style><style name="TpVideoSpeedItem">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:textColor">#FFFFFFFF</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/tp_speed_item_background</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:clickable">true</item>
        <item name="android:focusable">true</item>
        <item name="android:layout_marginBottom">4dp</item>
    </style><style name="TpVideoSpeedDropdownItem">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">40dp</item>
        <item name="android:textColor">#FFFFFFFF</item>
        <item name="android:textSize">13sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/tp_speed_item_background</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:clickable">true</item>
        <item name="android:focusable">true</item>
        <item name="android:layout_marginBottom">2dp</item>
        <item name="android:paddingHorizontal">12dp</item>
    </style></file><file path="C:\hhx\rk3588\AndroidStudio\VideoTest\TouptekSDK\src\main\res\values-night\tp_video_colors.xml" qualifiers="night-v8"><color name="tp_video_button_bg_normal">#FF1A1A1A</color><color name="tp_video_button_bg_pressed">#FF2A2A2A</color><color name="tp_video_button_bg_disabled">#FF0A0A0A</color><color name="tp_video_play_button_bg_normal">#FF000000</color><color name="tp_video_play_button_bg_pressed">#FF333333</color><color name="tp_video_button_stroke_normal">#4DFFFFFF</color><color name="tp_video_button_stroke_pressed">#66FFFFFF</color><color name="tp_video_button_stroke_disabled">#33FFFFFF</color><color name="tp_video_text_normal">#FFFFFFFF</color><color name="tp_video_text_disabled">#66FFFFFF</color><color name="tp_video_progress_bg">#33FFFFFF</color><color name="tp_video_progress_secondary">#66FFFFFF</color><color name="tp_video_progress_primary">#FFFFFFFF</color><color name="tp_video_progress_thumb">#FFFFFFFF</color><color name="tp_video_progress_thumb_stroke">#FF000000</color></file><file name="dialog_tp_test" path="C:\hhx\rk3588\AndroidStudio\VideoTest\TouptekSDK\src\main\res\layout\dialog_tp_test.xml" qualifiers="" type="layout"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\hhx\rk3588\AndroidStudio\VideoTest\TouptekSDK\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\hhx\rk3588\AndroidStudio\VideoTest\TouptekSDK\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\hhx\rk3588\AndroidStudio\VideoTest\TouptekSDK\build\generated\res\resValues\release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\hhx\rk3588\AndroidStudio\VideoTest\TouptekSDK\build\generated\res\resValues\release"/></dataSet><mergedItems><configuration qualifiers=""><declare-styleable name="TpVideoPlayerView">
        <attr format="boolean" name="autoPlay"/>
    </declare-styleable></configuration></mergedItems></merger>